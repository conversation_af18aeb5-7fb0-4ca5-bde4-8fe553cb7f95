package com.cosfo.mall.payment.controller;

import com.cosfo.mall.trade.adapter.dinpay.DinPayNotificationHandler;
import com.cosfo.mall.trade.model.response.NotificationResult;
import com.cosfo.mall.trade.service.PaymentNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 智付支付回调控制器
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/payment/notify")
public class DinPayNotifyController {

    @Autowired
    private PaymentNotificationService paymentNotificationService;

    /**
     * 智付支付回调
     */
    @PostMapping("/dinpay")
    public String handleDinPayNotify(@RequestBody String notifyData, HttpServletRequest request) {
        try {
            log.info("收到智付支付回调通知：{}", notifyData);
            
            // 处理回调通知
            NotificationResult result = paymentNotificationService.handlePaymentNotification(
                    "DINPAY", notifyData, request);
            
            if (result.isSuccess()) {
                log.info("智付支付回调处理成功");
                return "success";
            } else {
                log.error("智付支付回调处理失败：{}", result.getMessage());
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("智付支付回调处理异常", e);
            return "fail";
        }
    }

    /**
     * 智付退款回调
     */
    @PostMapping("/dinpay/refund")
    public String handleDinPayRefundNotify(@RequestBody String notifyData, HttpServletRequest request) {
        try {
            log.info("收到智付退款回调通知：{}", notifyData);
            
            // 处理退款回调通知
            NotificationResult result = paymentNotificationService.handleRefundNotification(
                    "DINPAY", notifyData, request);
            
            if (result.isSuccess()) {
                log.info("智付退款回调处理成功");
                return "success";
            } else {
                log.error("智付退款回调处理失败：{}", result.getMessage());
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("智付退款回调处理异常", e);
            return "fail";
        }
    }
}
