package com.cosfo.mall.payment.model.request;

import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.po.Refund;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 退款请求对象
 * @author: George
 * @date: 2023-09-02
 **/
@Data
public class RefundRequest {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 售后id
     */
    private Long orderAfterSaleId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 逆向分账退款明细
     */
    private List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList;

    /**
     * 退款信息
     */
    private Refund refund;
}
