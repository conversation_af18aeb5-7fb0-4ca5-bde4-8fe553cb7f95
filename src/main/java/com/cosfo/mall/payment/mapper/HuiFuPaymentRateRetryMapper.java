package com.cosfo.mall.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.payment.model.po.HuiFuPaymentRateRetry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface HuiFuPaymentRateRetryMapper extends BaseMapper<HuiFuPaymentRateRetry> {

    /**
     * 增加该重试数据的重试次数
     * @param id
     * @return
     */
    int increaseRetryNum(Long id);

    /**
     * 查询需要重试的数据(最多一次返回50条)
     * @param startTime
     * @param endTime
     * @return
     */
    List<HuiFuPaymentRateRetry> selectListByTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}