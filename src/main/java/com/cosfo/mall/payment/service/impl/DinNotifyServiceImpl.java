package com.cosfo.mall.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.PayNotifyMessageDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.service.DinNotifyService;
import com.cosfo.mall.payment.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.model.domain.CompanyAccount;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinPayNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.PaymentAttachInfoDTO;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.utils.SignUtils;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-08-20
 **/
@Service
@Slf4j
public class DinNotifyServiceImpl implements DinNotifyService {

    @Resource
    private MqProducer mqProducer;
    @Resource
    private PaymentService paymentService;

    @Override
    public String payNotify(DinNotifyDTO dinNotifyDTO) {
        log.info("收到智付支付回调:{}", dinNotifyDTO);

        // 校验回调数据
        validateNotifyParams(dinNotifyDTO);

        // 解析数据
        DinPayNotifyDTO data = parseData(dinNotifyDTO.getData(), DinPayNotifyDTO.class);

        // 解析密钥配置信息
        DinPayConfig dinPayConfig = parseSecretKey(data);

        // 验签
        Boolean verify = SignUtils.verifySign4DinPay(dinPayConfig, dinNotifyDTO.getData(), dinNotifyDTO.getSign());
        if (!verify) {
            return DinPaymentEnum.responseCode.SIGN_ERROR.getCode();
        }

        // 业务处理
        boolean result = paymentService.dinNotifyPaySuccess(data);

        log.info("支付单:{}智付回调处理成功", data.getOrderNo());

        // 支付mq通知支付结果
        if (result) {
            sendPayNotifyMessage(data.getOrderNo());
        }

        // 返回结果
        return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();
    }

    /**
     * 发送支付成功消息
     *
     * @param paymentNo
     */
    private void sendPayNotifyMessage(String paymentNo) {
        PayNotifyMessageDTO payNotifyDTO = new PayNotifyMessageDTO();
        payNotifyDTO.setPaymentNo(paymentNo);
        mqProducer.sendOrderly(MQTopicConstant.TOPIC_SAAS_PAYMENT_NOTIFY, null, payNotifyDTO, paymentNo);
    }

    /**
     * 校验回调参数
     *
     * @param dinNotifyDTO
     */
    private void validateNotifyParams(DinNotifyDTO dinNotifyDTO) {
        if (dinNotifyDTO == null) {
            throw new ParamsException("智付回调参数为空");
        }
        if (dinNotifyDTO.getData() == null) {
            throw new ParamsException("智付回调参数data为空");
        }
        if (dinNotifyDTO.getMerchantId() == null) {
            throw new ParamsException("智付回调参数merchantId为空");
        }
        if (dinNotifyDTO.getSignatureMethod() == null) {
            throw new ParamsException("智付回调参数signatureMethod为空");
        }
        if (dinNotifyDTO.getSign() == null) {
            throw new ParamsException("智付回调参数sign为空");
        }
    }

    /**
     * 解析数据
     *
     * @param data
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> T parseData(String data, Class<T> clazz) {
        try {
            return JSONObject.parseObject(data, clazz);
        } catch (Exception e) {
            log.error("智付回调数据解析异常", e);
            throw new ParamsException("智付回调数据解析异常");
        }
    }

    /**
     * 从订单备注字段解析出原交易时的密钥信息
     * 避免密钥紊乱
     *
     * @param data
     * @return
     */
    private DinPayConfig parseSecretKey(DinPayNotifyDTO data) {
        String orderDesc = data.getOrderDesc();
        PaymentAttachInfoDTO attachInfoDTO = JSONObject.parseObject(orderDesc, PaymentAttachInfoDTO.class);
        Integer paymentChannelId = attachInfoDTO.getPaymentChannelId();
        //TODO：George 2025/8/20 根据支付渠道ID获取密钥配置
        return new DinPayConfig();
    }
}
