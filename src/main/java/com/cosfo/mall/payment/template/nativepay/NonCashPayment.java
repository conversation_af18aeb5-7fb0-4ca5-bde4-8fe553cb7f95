package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.common.exception.PayBizException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckBiz;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationParamBO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationResultBO;
import com.cosfo.mall.order.model.bo.OrderBalanceBO;
import com.cosfo.mall.order.model.bo.OrderItemNonCashCalculationParamBO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.tenant.model.po.TenantFundAccountConfig;
import com.cosfo.mall.tenant.service.TenantFundAccountConfigService;
import com.cosfo.mall.tenant.service.TenantNonCashCalculationService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.usercenter.client.merchant.enums.MerchantPaymentAuthorityEnums;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-04-21
 **/
@Slf4j
@Service
public class NonCashPayment extends NativePayment {

    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private MerchantStoreBalanceChangeRecordService merchantStoreBalanceChangeRecordService;
    @Resource
    private TenantNonCashCalculationService tenantNonCashCalculationService;

    @Override
    protected void preProcess(PaymentRequest paymentRequest) {
        super.preProcess(paymentRequest);
        // 其他非现金支付的预处理逻辑
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStoreDtoInfo(loginContextInfoDTO.getStoreId());
        AssertCheckBiz.isTrue(MerchantPaymentAuthorityEnums.NonCashAuthority.OPEN.getAuthority().equals(merchantStoreDTO.getNonCashAuthority()), ResultDTOEnum.NON_CASH_SWITCH.getCode(), ResultDTOEnum.NON_CASH_SWITCH.getMessage());
        // 验证订单中存在不可支付的商品或者运费
        OrderNonCashCalculationResultBO orderNonCashCalculationResultBO = tenantNonCashCalculationService.calculateNonCashAmount(paymentRequest.getOrderNos());
        if (orderNonCashCalculationResultBO.isHasUnusableItems()) {
            throw new PayBizException("当前订单中存在不可支付的商品或者运费，请选择其他支付方式");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderPaymentTransaction(OrderBalanceBO orderBalanceBO) {
        // 非现金支付,还要再扣除非现金账户余额
        decreaseOrderNonCashBalance(orderBalanceBO);
        super.processOrderPaymentTransaction(orderBalanceBO);
    }

    private void decreaseOrderNonCashBalance(OrderBalanceBO orderBalanceBO) {
        Order order = orderBalanceBO.getOrder();
        BigDecimal totalPrice = order.getPayablePrice();

        // 查询非现金账户
        List<MerchantStoreBalance> merchantStoreBalances = merchantStoreBalanceService.queryNonCashAccountByStoreIdForUpdate(order.getTenantId(), order.getStoreId());
        if (CollectionUtils.isEmpty(merchantStoreBalances)) {
            throw new PayBizException("当前账户金额不足，请选择其他支付方式");
        }
        // 计算非现金账户总金额
        BigDecimal totalAmount = merchantStoreBalances.stream()
                .map(MerchantStoreBalance::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalAmount.compareTo(totalPrice) < 0) {
            throw new PayBizException("当前现金账户金额不足，请选择其他支付方式");
        }

        // 扣减或者冻结
        Integer decreaseType = orderBalanceBO.getDecreaseType();

        // 循环遍历非现金账户，逐个扣减，并记录变动记录
        BigDecimal remainingAmount = totalPrice;
        for (MerchantStoreBalance storeBalance : merchantStoreBalances) {
            // 如果剩余需扣减金额为0，结束循环
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal currentBalance = storeBalance.getBalance();
            // 跳过余额为 0 的账户
            if (currentBalance.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算当前账户需要扣减的金额
            BigDecimal deductAmount;
            if (currentBalance.compareTo(remainingAmount) >= 0) {
                // 账户余额足够支付剩余金额
                deductAmount = remainingAmount;
                remainingAmount = BigDecimal.ZERO;
            } else {
                // 账户余额不足，扣减全部余额
                deductAmount = currentBalance;
                remainingAmount = remainingAmount.subtract(currentBalance);
            }

            if (Objects.equals(decreaseType, MerchantStoreBalanceEnums.DecreaseTypeEnum.FREEZE.getType())) {
                // 冻结余额
                int decreased = merchantStoreBalanceService.freezeBalance(
                        storeBalance.getId(),
                        deductAmount);
                if (decreased != 1) {
                    throw new PayBizException("当前账户金额不足，请选择其他支付方式");
                }
                log.info("订单号：{}，冻结非现金账户余额成功，冻结金额：{}", order.getOrderNo(), deductAmount);
                // 创建余额变动记录
                merchantStoreBalanceChangeRecordService.generateBalanceChangeRecordByOrder(order.getOrderNo(), deductAmount, MerchantStoreBalanceChangeRecordTypeEnum.FROZEN.getType(), storeBalance);
                continue;
            }

            // 扣减余额
            int decreased = merchantStoreBalanceService.decreaseBalance(
                    storeBalance.getId(),
                    deductAmount
            );

            if (decreased != 1) {
                throw new PayBizException("当前账户金额不足，请选择其他支付方式");
            }
            log.info("订单号：{}，扣减非现金账户余额成功，扣减金额：{}", order.getOrderNo(), deductAmount);

            // 创建余额变动记录
            merchantStoreBalanceChangeRecordService.generateBalanceChangeRecordByOrder(order.getOrderNo(), deductAmount, MerchantStoreBalanceChangeRecordTypeEnum.CONSUMPTION.getType(), storeBalance);
        }

        // 如果最后还有剩余金额未扣减，抛出异常
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            throw new PayBizException("当前账户金额不足，请选择其他支付方式");
        }
    }
}
