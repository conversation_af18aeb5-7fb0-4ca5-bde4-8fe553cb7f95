package com.cosfo.mall.payment.template.nativepay;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.order.converter.OrderConverter;
import com.cosfo.mall.order.model.bo.OrderBalanceBO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 账期和余额支付的抽象类
 * 两种支付方式的相似度极高,故抽象出相似逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class NativePayment extends PayTemplate {

    @Resource
    private TenantPrepaymentService tenantPrepaymentService;
    @Resource
    private OrderService orderService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public PaymentResult processPay(PaymentRequest request) {

        // 拆分合并订单和独立订单
        Map<Long, List<Order>> combinedOrders = new HashMap<>();
        List<Order> commonOrders = new ArrayList<>();

        request.getOrders().forEach(orderDTO -> {
            Order order = OrderConverter.INSTANCE.dtoToOrder(orderDTO);
            if (order.getOrderType().equals(OrderEnums.OrderType.COMBINE.getCode())) {
                combinedOrders.computeIfAbsent(order.getCombineOrderId(), k -> new ArrayList<>())
                        .add(order);
            } else {
                commonOrders.add(order);
            }
        });

        // 组合支付是冻结 非组合支付则扣除
        Integer decreaseType = request.isCombineRequest() ?
                MerchantStoreBalanceEnums.DecreaseTypeEnum.FREEZE.getType() :
                MerchantStoreBalanceEnums.DecreaseTypeEnum.DECREASE.getType();

        // 支付流程:
        PaymentResult paymentResult = new PaymentResult();

        // 1. 先支付合并订单
        transactionTemplate.execute(status -> {
            Set<Long> combineOrderIds = combinedOrders.keySet();
            for (Long combineOrderId : combineOrderIds) {
                List<Order> orderList = combinedOrders.get(combineOrderId);
                processPaymentForListOfOrders(orderList, decreaseType);
            }

            // 2. 再支付独立订单
            processPaymentForListOfOrders(commonOrders, decreaseType);
            paymentResult.setSuccess(true);
            paymentResult.setPaymentReceipt(request.getPaymentReceipt());

            // 3.如果组合支付 支付单改冻结
            if (request.isCombineRequest()) {
                // 更新子支付单的状态为冻结中
                Long paymentId = request.getPaymentId();
                int updateStatus = paymentCombinedDetailService.updateStatus(
                        paymentId,
                        PaymentEnum.Status.FREEZE.getCode(),
                        PaymentEnum.Status.WAITING.getCode());
                if (updateStatus != 1) {
                    log.error("支付单：[{}]由待支付变更冻结中异常", paymentId);
                    throw new ProviderException("本次支付交易失败，请稍后再试");
                }
            }
            return null;
        });
        return paymentResult;
    }


    @Override
    protected void verifyLastPaymentStatus(PaymentRequest request) {
        // do nothing;
    }

    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        return null;
    }

    @Override
    public boolean callClosePayRequest(PaymentRequest paymentRequest) {
        return false;
    }

    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        // 如果是组合支付 没有成功操作
        if (request.isCombineRequest()) {
            return;
        }

        Long paymentId = request.getPaymentId();
        // 乐观更新支付单为成功
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.SUCCESS.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成功异常", paymentId);
            throw new ProviderException("本次支付交易失败，请稍后再试");
        }
        // 更新支付成功状态
        request.getOrders().forEach(order -> {
            orderService.updatePaySuccess(order.getId());
        });
    }

    /**
     * 支付列表里的所有订单
     *
     * @param orders 订单list
     */
    public void processPaymentForListOfOrders(List<Order> orders, Integer decreaseType) {
        // 获取该订单列表的总价和所有订单号
        BigDecimal needPayPrice = orders.stream().map(Order::getPayablePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 先支付无仓和自营仓订单,再支付三方仓订单
        List<Order> proprietaryOrders = new ArrayList<>();
        List<Order> thirdPartyOrders = new ArrayList<>();

        orders.forEach(order -> {
            if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(order.getWarehouseType())) {
                thirdPartyOrders.add(order);
            } else {
                proprietaryOrders.add(order);
            }
        });

        List<Order> orderList = new ArrayList<>();
        orderList.addAll(proprietaryOrders);
        orderList.addAll(thirdPartyOrders);

        for (Order order : orderList) {
            OrderBalanceBO orderBalanceBO = convertOrderToOrderBalanceBo(order, needPayPrice, decreaseType);
            processOrderPaymentTransaction(orderBalanceBO);
            needPayPrice = NumberUtil.sub(needPayPrice, order.getPayablePrice());
        }
    }

    /**
     * 单个支付单的支付事务
     *
     * @param orderBalanceBO 支付订单业务对象
     */
//    @Transactional(rollbackFor = Exception.class)
    public void processOrderPaymentTransaction(OrderBalanceBO orderBalanceBO) {
        Order order = orderBalanceBO.getOrder();
        if (order == null) {
            return;
        }

        // 如果是三方订单,则扣减预付
        if (order.getWarehouseType().equals(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            tenantPrepaymentService.dealThreePartiesOrderPrepayment(orderBalanceBO);
        }
    }


    private OrderBalanceBO convertOrderToOrderBalanceBo(Order order,
                                                        BigDecimal needPayPrice, Integer decreaseType) {
        OrderBalanceBO orderBalanceBO = new OrderBalanceBO();
        orderBalanceBO.setOrder(order);
        orderBalanceBO.setNeedPayPrice(needPayPrice);
        orderBalanceBO.setDecreaseType(decreaseType);
        return orderBalanceBO;
    }

    @Override
    protected void paySuccess(PaymentRequest request, PaymentResult result) {

    }
}
