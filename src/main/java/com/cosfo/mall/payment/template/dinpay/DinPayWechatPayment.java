package com.cosfo.mall.payment.template.dinpay;

import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.trade.model.common.PayerInfo;
import com.cosfo.mall.trade.model.config.ChannelConfig;
import com.cosfo.mall.trade.model.request.UnifiedPaymentRequest;
import com.cosfo.mall.trade.model.response.UnifiedPaymentResult;
import com.cosfo.mall.trade.service.PaymentClientService;
import com.cosfo.mall.common.context.UserLoginContextUtil;
import com.cosfo.mall.common.context.dto.LoginContextInfoDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 智付微信支付模板
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
public class DinPayWechatPayment extends PayTemplate {

    @Autowired
    private PaymentClientService paymentClientService;
    
    @Autowired
    private TenantService tenantService;

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        try {
            // 1. 构建统一支付请求
            UnifiedPaymentRequest unifiedRequest = buildUnifiedPaymentRequest(request);
            
            // 2. 调用统一支付服务
            UnifiedPaymentResult unifiedResult = paymentClientService.pay(unifiedRequest);
            
            // 3. 转换为系统标准的PaymentResult
            return convertToPaymentResult(unifiedResult, request);
            
        } catch (Exception e) {
            log.error("智付微信支付处理失败，订单号：{}，错误：{}", request.getPaymentNo(), e.getMessage(), e);
            PaymentResult result = new PaymentResult();
            result.setSuccess(false);
            return result;
        }
    }

    /**
     * 构建统一支付请求
     */
    private UnifiedPaymentRequest buildUnifiedPaymentRequest(PaymentRequest request) {
        // 获取租户配置
        LoginContextInfoDTO loginContext = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantConfig = tenantService.queryTenantAuthConnection(loginContext.getTenantId());
        
        // 构建渠道配置
        ChannelConfig channelConfig = buildChannelConfig(tenantConfig, request);
        
        // 构建支付者信息
        PayerInfo payerInfo = PayerInfo.builder()
                .userId(request.getOpenId())
                .ip(getClientIp(request))
                .build();
        
        // 确定平台类型
        String platform = request.getH5Request() ? 
                PaymentDictionaryEnums.Platform.H5.getName() : 
                PaymentDictionaryEnums.Platform.MINI_APP.getName();
        
        return UnifiedPaymentRequest.builder()
                .paymentNo(request.getPaymentNo())
                .totalAmount(request.getTransAmt().multiply(new java.math.BigDecimal("100")).longValue()) // 转换为分
                .currency("CNY")
                .description(buildDescription(request))
                .paymentMethod(PaymentMethodEnums.WECHAT.getCode())
                .platform(platform)
                .notifyUrl(buildNotifyUrl())
                .timeExpireSeconds("1800") // 30分钟过期
                .channelConfig(channelConfig)
                .payer(payerInfo)
                .build();
    }

    /**
     * 构建渠道配置
     */
    private ChannelConfig buildChannelConfig(TenantAuthConnectionDTO tenantConfig, PaymentRequest request) {
        // 构建智付特有配置
        Map<String, Object> extraConfig = new HashMap<>();
        extraConfig.put("domain", getDinPayDomain());
        extraConfig.put("payUrl", getDinPayUrl(request.getH5Request()));
        extraConfig.put("queryUrl", "/api/appPay/payQuery");
        extraConfig.put("closeUrl", "/api/appPay/payClose");
        extraConfig.put("refundUrl", "/api/appPay/payRefund");
        extraConfig.put("queryRefundUrl", "/api/appPay/payRefundQuery");
        extraConfig.put("miniAppId", tenantConfig.getDinpayMiniAppId());
        extraConfig.put("appId", tenantConfig.getDinpayAppId());
        
        return ChannelConfig.builder()
                .channelCode(PaymentChannelProviderEnums.DIN_PAY.name())
                .channelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName())
                .merchantNo(tenantConfig.getDinpayMerchantNo())
                .secretKey(tenantConfig.getDinpaySecret())
                .publicKey(tenantConfig.getDinpayPublicKey())
                .privateKey(tenantConfig.getDinpayPrivateKey())
                .extraConfig(extraConfig)
                .build();
    }

    /**
     * 转换为系统标准的PaymentResult
     */
    private PaymentResult convertToPaymentResult(UnifiedPaymentResult unifiedResult, PaymentRequest request) {
        PaymentResult result = new PaymentResult();
        result.setSuccess(unifiedResult.isSuccess());
        
        if (unifiedResult.isSuccess() && unifiedResult.getCredential() != null) {
            // 提取支付凭证信息
            Map<String, Object> extraData = unifiedResult.getCredential().getExtraData();
            if (extraData != null) {
                result.setOaAppId((String) extraData.get("appId"));
                result.setTimeStamp((String) extraData.get("timeStamp"));
                result.setNonceStr((String) extraData.get("nonceStr"));
                result.setPackageStr((String) extraData.get("package"));
                result.setSignType((String) extraData.get("signType"));
                result.setPaySign((String) extraData.get("paySign"));
                result.setPrepayId(unifiedResult.getCredential().getContent());
            }
        }
        
        return result;
    }

    /**
     * 构建商品描述
     */
    private String buildDescription(PaymentRequest request) {
        if (request.getOrders() != null && !request.getOrders().isEmpty()) {
            return "订单支付-" + request.getOrders().get(0).getOrderNo();
        }
        return "商品支付";
    }

    /**
     * 构建回调地址
     */
    private String buildNotifyUrl() {
        // TODO: 根据环境配置返回正确的回调地址
        return "https://your-domain.com/payment/notify/dinpay";
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(PaymentRequest request) {
        // TODO: 从请求中获取真实IP
        return "127.0.0.1";
    }

    /**
     * 获取智付域名
     */
    private String getDinPayDomain() {
        // TODO: 根据环境配置返回正确的域名
        return "https://paymenttest.dinpay.com/trx"; // 测试环境
    }

    /**
     * 获取智付支付URL
     */
    private String getDinPayUrl(boolean isH5) {
        return isH5 ? "/api/appPay/payPublic" : "/api/appPay/payApplet";
    }
}
