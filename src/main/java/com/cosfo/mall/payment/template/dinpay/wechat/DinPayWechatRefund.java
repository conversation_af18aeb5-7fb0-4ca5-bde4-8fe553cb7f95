package com.cosfo.mall.payment.template.dinpay.wechat;

import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.context.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.template.dinpay.DinPayRefundTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 智付微信退款模板
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
public class DinPayWechatRefund extends DinPayRefundTemplate {

    @Resource
    private TenantService tenantService;

    @Override
    protected void preProcess(RefundExecuteRequest request) {
        super.preProcess(request);

        // 校验智付微信退款权限
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());

        // TODO: 检查智付微信退款开关
        Integer dinpayWechatSwitch = tenantAuthConnectionDTO.getDinpaySwitch();
        if (PaymentEnum.Switch.CLOSE.getType().equals(dinpayWechatSwitch)) {
            throw new BizException("您暂无智付微信退款权限");
        }

        log.info("智付微信退款预处理完成 - 租户ID：{}，退款单ID：{}",
                loginContextInfoDTO.getTenantId(), request.getRefund().getId());
    }

    @Override
    protected Object getDinPayConfig(Long tenantId) {
        // TODO: 统一提供智付配置获取方法
        TenantAuthConnectionDTO tenantConfig = tenantService.queryTenantAuthConnection(tenantId);

        // 构建智付配置对象
        // 这里先返回租户配置，后续统一提供专门的智付配置类
        return tenantConfig;
    }
}
