package com.cosfo.mall.payment.template.dinpay;

import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.model.common.PayerInfo;
import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.service.PaymentClientService;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * 智付支付基础模板
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
public abstract class DinPaymentTemplate extends PayTemplate {

    @Resource
    private PaymentClientService paymentClientService;
    @Resource
    private PaymentMapper paymentMapper;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    /**
     * 获取智付配置
     * TODO: 统一提供智付配置获取方法
     */
    protected Object getDinPayConfig(Long tenantId) {
        return null;
    }

    /**
     * 获取支付方式类型
     */
    protected abstract String getPaymentMethod();

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        try {
            log.info("开始智付支付处理 - 订单号：{}，支付方式：{}",
                    request.getPaymentNo(), getPaymentMethod());

            // 1. 构建统一支付请求
            UnifiedPaymentRequest unifiedRequest = buildUnifiedPaymentRequest(request);

            // 2. 调用统一支付服务
            UnifiedPaymentResult unifiedResult = paymentClientService.pay(unifiedRequest);

            // 3. 转换为系统标准的PaymentResult
            PaymentResult result = convertToPaymentResult(unifiedResult, request);

            log.info("智付支付处理完成 - 订单号：{}，结果：{}", request.getPaymentNo(), result.isSuccess());
            return result;

        } catch (Exception e) {
            log.error("智付支付处理失败，订单号：{}，错误：{}", request.getPaymentNo(), e.getMessage(), e);
            PaymentResult result = new PaymentResult();
            result.setSuccess(false);
            return result;
        }
    }

    /**
     * 构建统一支付请求
     */
    private UnifiedPaymentRequest buildUnifiedPaymentRequest(PaymentRequest request) {
        // 获取租户配置
        Object dinPayConfig = getDinPayConfig(request.getTenantId());

        // 构建渠道配置
        ChannelConfig channelConfig = buildChannelConfig(dinPayConfig, request);

        Long paymentId = request.getPaymentId();
        if (request.isCombineRequest()) {
            paymentId = request.getMasterPaymentId();
        }
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);

        // 构建支付者信息
        PayerInfo payerInfo = PayerInfo.builder()
                .userId(payment.getSpOpenid())
                .build();

        return UnifiedPaymentRequest.builder()
                .paymentNo(request.getPaymentNo())
                .totalAmount(request.getTransAmt().multiply(new BigDecimal("100")).intValue())
                .currency("CNY")
                .description(request.getPaymentDesc())
                .paymentMethod(getPaymentMethod())
                .platform(request.getH5Request() ? PaymentDictionaryEnums.Platform.H5.getName() : PaymentDictionaryEnums.Platform.MINI_APP.getName())
                .notifyUrl(notifyDomain + "/pay-notify/din-pay")
                .timeExpireSeconds("1770")
                .channelConfig(channelConfig)
                .payer(payerInfo)
                .build();
    }

    /**
     * 构建渠道配置
     */
    private ChannelConfig buildChannelConfig(Object dinPayConfig, PaymentRequest request) {
        // TODO: 根据统一提供的智付配置方法来构建
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setMerchantNo("1");
        channelConfig.setSecretKey("2");
        channelConfig.setPublicKey("3");
        channelConfig.setPrivateKey("4");
        return channelConfig;
    }

    /**
     * 转换为系统标准的PaymentResult
     */
    private PaymentResult convertToPaymentResult(UnifiedPaymentResult unifiedResult, PaymentRequest request) {
        PaymentResult result = new PaymentResult();
        result.setSuccess(unifiedResult.isSuccess());

        if (unifiedResult.isSuccess() && unifiedResult.getCredential() != null) {
            // 提取支付凭证信息
            Map<String, String> extraData = unifiedResult.getCredential().getExtraData();
            if (extraData != null) {
                result.setOaAppId(extraData.get("appId"));
                result.setTimeStamp(extraData.get("timeStamp"));
                result.setNonceStr((extraData.get("nonceStr")));
                result.setPackageStr(extraData.get("package"));
                result.setSignType(extraData.get("signType"));
                result.setPaySign(extraData.get("paySign"));
                result.setPrepayId(unifiedResult.getCredential().getContent());
                result.setQrCode(extraData.get("qrCode"));
            }
        }

        return result;
    }

    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        // 如果是组合支付 则冻结支付单即可 等待后续支付回调或者订单超时取消处理最终结果
        if (request.isCombineRequest()) {
            // 更新子支付单的状态为冻结中
            Long paymentId = request.getPaymentId();
            int updateStatus = paymentCombinedDetailService.updateStatus(
                    paymentId,
                    PaymentEnum.Status.DEALING.getCode(),
                    PaymentEnum.Status.WAITING.getCode());
            if (updateStatus != 1) {
                log.error("支付单：[{}]由待支付变更程处理中异常", paymentId);
                throw new ProviderException("本次支付交易失败，请稍后再试");
            }
            return;
        }

        Long paymentId = request.getPaymentId();
        // 乐观更新支付单为锁定状态
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.DEALING.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成处理中失败", paymentId);
            throw new ProviderException("本次支付交易失败，请稍后再试");
        }
    }

    @Override
    protected void paySuccess(PaymentRequest request, PaymentResult result) {

    }

    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        try {
            log.info("开始智付支付查询 - 支付单ID：{}", paymentRequest.getPaymentId());

            // 1. 构建统一查询请求
            UnifiedQueryPaymentRequest queryRequest = buildUnifiedQueryRequest(paymentRequest);

            // 2. 调用统一查询服务
            UnifiedQueryPaymentResult queryResult = paymentClientService.queryPayment(queryRequest);

            // 3. 转换为系统标准的PaymentResult
            PaymentResult result = convertQueryResultToPaymentResult(queryResult, paymentRequest);

            log.info("智付支付查询完成 - 支付单ID：{}，状态：{}",
                    paymentRequest.getPaymentId(),
                    result != null && result.getOrderPayResultDTO() != null ?
                            result.getOrderPayResultDTO().getPaymentStatus() : "null");
            return result;

        } catch (Exception e) {
            log.error("智付支付查询失败 - 支付单ID：{}，错误：{}",
                    paymentRequest.getPaymentId(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean callClosePayRequest(PaymentRequest paymentRequest) {
        try {
            log.info("开始智付关单 - 订单号：{}", paymentRequest.getPaymentNo());

            // 构建统一关单请求
            UnifiedClosePaymentRequest closeRequest = buildUnifiedCloseRequest(paymentRequest);

            // 调用统一关单服务
            UnifiedClosePaymentResult closeResult = paymentClientService.closePayment(closeRequest);

            if (closeResult.isSuccess()) {
                log.info("智付关单成功 - 订单号：{}", paymentRequest.getPaymentNo());
                return true;
            } else {
                log.error("智付关单失败 - 订单号：{}，错误：{}",
                        paymentRequest.getPaymentNo(), closeResult.getError());
                return false;
            }

        } catch (Exception e) {
            log.error("智付关单异常 - 订单号：{}，错误：{}",
                    paymentRequest.getPaymentNo(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建统一关单请求
     */
    private UnifiedClosePaymentRequest buildUnifiedCloseRequest(PaymentRequest paymentRequest) {
        // 获取租户配置
        Object dinPayConfig = getDinPayConfig(paymentRequest.getTenantId());

        // 构建渠道配置
        ChannelConfig channelConfig = buildChannelConfig(dinPayConfig, paymentRequest);

        return UnifiedClosePaymentRequest.builder()
                .paymentNo(paymentRequest.getPaymentNo())
                .tenantId(paymentRequest.getTenantId())
                .channelConfig(channelConfig)
                .build();
    }

    /**
     * 构建统一查询请求
     */
    private UnifiedQueryPaymentRequest buildUnifiedQueryRequest(PaymentRequest paymentRequest) {
        // 获取支付单信息
        Payment payment = paymentMapper.selectByPrimaryKey(paymentRequest.getPaymentId());
        if (payment == null) {
            log.error("支付单不存在 - 支付单ID：{}", paymentRequest.getPaymentId());
            throw new RuntimeException("支付单不存在");
        }

        // 获取智付配置
        Object dinPayConfig = getDinPayConfig(paymentRequest.getTenantId());
        ChannelConfig channelConfig = buildChannelConfig(dinPayConfig, paymentRequest);

        return UnifiedQueryPaymentRequest.builder()
                .paymentNo(payment.getPaymentNo())
                .tenantId(paymentRequest.getTenantId())
                .channelConfig(channelConfig)
                .build();
    }

    /**
     * 转换查询结果为PaymentResult
     */
    private PaymentResult convertQueryResultToPaymentResult(UnifiedQueryPaymentResult queryResult, PaymentRequest paymentRequest) {
        if (queryResult == null) {
            log.warn("智付查询结果为空 - 支付单ID：{}", paymentRequest.getPaymentId());
            return null;
        }

        // 构建OrderPayResultDTO
        OrderPayResultDTO orderPayResultDTO = new OrderPayResultDTO();
        orderPayResultDTO.setPaymentId(paymentRequest.getPaymentId());

        // 将智付状态映射为系统状态
        PaymentEnum.Status systemStatus = mapDinPayStatusToSystemStatus(queryResult.getStatus());
        orderPayResultDTO.setPaymentStatus(systemStatus.getCode());

        PaymentResult result = new PaymentResult();
        result.setOrderPayResultDTO(orderPayResultDTO);
        return result;
    }

    /**
     * 将智付支付状态映射为系统状态
     */
    private PaymentEnum.Status mapDinPayStatusToSystemStatus(PaymentStatus dinPayStatus) {
        if (dinPayStatus == null) {
            return PaymentEnum.Status.WAITING;
        }

        switch (dinPayStatus) {
            case SUCCESS:
                return PaymentEnum.Status.SUCCESS;
            case PENDING:
                return PaymentEnum.Status.DEALING;
            case FAILED:
                return PaymentEnum.Status.FAIL;
            case CANCELLED:
                return PaymentEnum.Status.CANCELED;
            default:
                log.warn("未知的智付支付状态：{}", dinPayStatus);
                return PaymentEnum.Status.WAITING;
        }
    }
}