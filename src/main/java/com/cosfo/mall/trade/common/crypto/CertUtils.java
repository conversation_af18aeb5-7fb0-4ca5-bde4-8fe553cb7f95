package net.summerfarm.payment.trade.common.crypto;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.codec.Base64Encoder;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Enumeration;

/**
 * 证书工具类
 */
public abstract class CertUtils {

    static Provider provider = null;

    static {
        provider = new BouncyCastleProvider();
        Security.addProvider(provider);
    }

    private CertUtils() {
    }

    public static PublicKey getPublicKey(String certFilePath) {
        try {
            return getX509Certificate(certFilePath).getPublicKey();
        } catch (Exception e) {
            throw new PaymentException(ErrorCode.CERTIFICATE_ERROR, "获取公钥失败: " + certFilePath, e);
        }
    }

    public static X509Certificate getX509Certificate(String certFilePath) throws CertificateException,
            NoSuchProviderException, IOException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509", provider);
        byte[] readAllBytes = Files.readAllBytes(Paths.get(certFilePath));
        String fileContent = new String(readAllBytes);
        if (!fileContent.contains("-----BEGIN CERTIFICATE-----")) {
            fileContent = "-----BEGIN CERTIFICATE-----\n" + fileContent +
                    "\n-----END CERTIFICATE-----";
        }
        InputStream is = new ByteArrayInputStream(fileContent.getBytes());
        return (X509Certificate) cf.generateCertificate(is);
    }

    public static PrivateKey getPrivateKey(String pfxPath, String pfxPassword) {
        try {
            if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
                Security.addProvider(new BouncyCastleProvider());
            }
            KeyStore ks = KeyStore.getInstance("PKCS12", BouncyCastleProvider.PROVIDER_NAME);
            try (FileInputStream fis = new FileInputStream(pfxPath)) {
                char[] nPassword = (pfxPassword == null || pfxPassword.trim().isEmpty()) ? null : pfxPassword.toCharArray();
                ks.load(fis, nPassword);
                Enumeration<String> enumas = ks.aliases();
                String keyAlias = null;
                if (enumas.hasMoreElements()) {
                    keyAlias = enumas.nextElement();
                }
                return (PrivateKey) ks.getKey(keyAlias, nPassword);
            }
        } catch (Exception e) {
            throw new PaymentException(ErrorCode.CERTIFICATE_ERROR, "获取私钥失败: " + pfxPath, e);
        }
    }

    public static String getPrivateKeyBase64(String pfxPath, String pfxPassword) {
        PrivateKey privateKey = getPrivateKey(pfxPath, pfxPassword);
        return Base64Encoder.encode(privateKey.getEncoded());
    }

    public static String getPublicKeyBase64(String certFilePath) throws CertificateException, IOException, NoSuchProviderException {
        PublicKey publicKey = getPublicKey(certFilePath);
        return Base64Encoder.encode(publicKey.getEncoded());
    }

    public static PublicKey getPublicKeyByBase64(String base64PublicKey) {
        try {
            byte[] keyBytes = Base64.decode(base64PublicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            throw new PaymentException(ErrorCode.CERTIFICATE_ERROR, "Base64字符串转换为公钥失败", e);
        }
    }

    public static PrivateKey getPrivateKeyByBase64(String base64PrivateKey) {
        try {
            byte[] keyBytes = Base64.decode(base64PrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            throw new PaymentException(ErrorCode.CERTIFICATE_ERROR, "Base64字符串转换为私钥失败", e);
        }
    }
}