package com.cosfo.mall.trade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-08
 **/
public class DinPaymentEnum {

    @Getter
    @AllArgsConstructor
    public enum domain {

        PRO("https://payment.dinpay.com/trx"),
        DEV("https://paymenttest.dinpay.com/trx"),
        ;

        private final String domain;

        public static String determineDomain(boolean pro) {
            return pro ? PRO.getDomain() : DEV.getDomain();
        }
    }

    @Getter
    @AllArgsConstructor
    public enum url {
        MINI_APP_PAY("/api/appPay/payApplet"),
        PUBLIC_ACCOUNT_PAY("/api/appPay/payPublic"),
        PAY_QUERY("/api/appPay/payQuery"),
        PAY_CLOSE("/api/appPay/payClose"),
        REFUND_QUERY("/api/appPay/payRefundQuery"),
        REFUND("/api/appPay/payRefund"),
        ;

        public static String determinePayUrl(boolean miniApp) {
            return miniApp ? MINI_APP_PAY.getUrl() : PUBLIC_ACCOUNT_PAY.getUrl();
        }

        private final String url;
    }

    @Getter
    @AllArgsConstructor
    public enum interfaceName {

        /**
         * 小程序
         */
        MINI_APP("AppPayApplet"),
        /**
         * 公众号
         */
        PUBLIC_ACCOUNT("AppPayPublic"),

        /**
         * 支付结果查询
         */
        APP_PAY_QUERY("AppPayQuery"),

        /**
         * 关闭订单
         */
        APP_PAY_CLOSE("AppPayClose"),

        /**
         * 退款
         */
        APP_PAY_REFUND("AppPayRefund"),

        /**
         * 退款查询
         */
        APP_PAY_REFUND_QUERY("AppPayRefundQuery"),
        ;

        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum paymentType {

        WECHAT("WXPAY"),
        ALIPAY("ALIPAY"),
        ;

        private final String type;
    }

    @Getter
    @AllArgsConstructor
    public enum paymentMethods {

        /**
         * 小程序
         */
        MINI_APP("APPLET"),
        /**
         * 公众号
         */
        PUBLIC("PUBLIC"),
        ;

        private final String method;
    }

    @Getter
    @AllArgsConstructor
    public enum responseCode {

        SUCCESS("0000", "成功"),

        REFUND_RECEIVE_SUCCESS("0001", "退款接收成功"),

        /**
         * 支付、退款回调处理成功
         */
        NOTIFY_PROCESS_SUCCESS("success", "通知处理成功"),

        ;
        private final String code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum orderStatus {

        INIT("INIT", "已接收"),
        DOING("DOING", "处理中"),
        SUCCESS("SUCCESS", "成功"),
        FAIL("FAIL", "失败"),
        CLOSE("CLOSE", "关闭"),
        CANCEL("CANCEL", "撤销"),
        ;

        private final String status;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum refundStatus {

        BEFORE_RECEIVE("BEFORERECEIVE", "等待处理"),
        RECEIVE("RECEIVE", "接收成功"),
        INIT("INIT", "初始化"),
        DOING("DOING", "处理中"),
        SUCCESS("SUCCESS", "成功"),
        FAIL("FAIL", "失败"),
        CLOSE("CLOSE", "关闭"),
        ;

        private final String status;
        private final String desc;
    }
}
