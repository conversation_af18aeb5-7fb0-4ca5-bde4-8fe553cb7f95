package com.cosfo.mall.trade.common.exception;

import lombok.Getter;
import net.summerfarm.payment.trade.common.enums.ErrorCode;

/**
 * 支付SDK通用异常
 */
@Getter
public class PaymentException extends RuntimeException {

    private final ErrorCode errorCode;
    private final String detailMessage;

    public PaymentException(ErrorCode errorCode, String detailMessage) {
        super(String.format("[%s] %s", errorCode.getCode(), detailMessage));
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }

    public PaymentException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(String.format("[%s] %s", errorCode.getCode(), detailMessage), cause);
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }
}