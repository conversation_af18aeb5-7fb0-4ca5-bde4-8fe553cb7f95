package com.cosfo.mall.trade.common.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;

import java.security.PublicKey;
import java.util.Objects;

/**
 * @description: 智付HTTP请求工具类
 * @author: George
 * @date: 2025-02-11
 */
@Slf4j
public class DinUtils {

    public static <T> DinResponseDTO<T> executeRequest(String url, String requestBody, Class<T> responseType, PublicKey publicKey) {
        String responseBody;
        try {
            responseBody = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(requestBody)
                    .timeout(30000) // 30 seconds timeout
                    .execute()
                    .body();
            log.debug("智付请求参数：{}，智付响应结果：{}", requestBody, responseBody);
        } catch (Exception e) {
            log.error("请求智付接口异常, url: {}, body: {}", url, requestBody, e);
            throw new PaymentException(ErrorCode.NETWORK_ERROR, "请求智付接口异常", e);
        }

        if (responseBody == null || responseBody.isEmpty()) {
            throw new PaymentException(ErrorCode.NETWORK_ERROR, "智付接口响应为空");
        }

        // 1. 初步解析响应，将data字段作为String
        DinResponseDTO<String> tempResult = JSON.parseObject(responseBody, new TypeReference<DinResponseDTO<String>>() {});

        // 2. 验签 - 只有在响应成功且有数据体时才进行
        if ("0000".equals(tempResult.getCode()) && Objects.nonNull(tempResult.getData())) {
            if (!SM2Utils.verify(publicKey, tempResult.getData(), tempResult.getSign())) {
                log.error("智付响应验签失败. Response: {}", responseBody);
                throw new PaymentException(ErrorCode.SIGNATURE_ERROR, "智付响应验签失败");
            }
        }

        // 3. 解析data字段为具体类型
        T realData = tempResult.getData() != null ? JSON.parseObject(tempResult.getData(), responseType) : null;

        // 4. 返回完整解析后的对象
        return new DinResponseDTO<>(tempResult.getCode(), tempResult.getMsg(), realData,
                tempResult.getSignatureMethod(), tempResult.getSign());
    }
}
