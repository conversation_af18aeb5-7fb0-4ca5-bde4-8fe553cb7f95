package net.summerfarm.payment.trade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道特性枚举
 * 用于声明渠道支持的功能特性
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Getter
@AllArgsConstructor
public enum FeatureType {

    /**
     * 实时分账
     */
    REAL_TIME_PROFIT_SHARING("REAL_TIME_PROFIT_SHARING", "实时分账"),

    /**
     * 延迟分账
     */
    DELAY_PROFIT_SHARING("DELAY_PROFIT_SHARING", "延迟分账"),

    /**
     * 支持一分钱订单
     */
    ALLOW_ONE_CENT_ORDER("ALLOW_ONE_CENT_ORDER", "一分钱订单"),

    /**
     * 支持退款
     */
    REFUND_SUPPORT("REFUND_SUPPORT", "支持退款"),

    /**
     * 支持部分退款
     */
    PARTIAL_REFUND_SUPPORT("PARTIAL_REFUND_SUPPORT", "支持部分退款"),

    /**
     * 支持订单查询
     */
    ORDER_QUERY_SUPPORT("ORDER_QUERY_SUPPORT", "支持订单查询"),

    /**
     * 支持关闭订单
     */
    ORDER_CLOSE_SUPPORT("ORDER_CLOSE_SUPPORT", "支持关闭订单"),

    /**
     * 支持异步通知
     */
    ASYNC_NOTIFY_SUPPORT("ASYNC_NOTIFY_SUPPORT", "支持异步通知"),

    /**
     * 支持同步返回
     */
    SYNC_RETURN_SUPPORT("SYNC_RETURN_SUPPORT", "支持同步返回"),

    /**
     * 支持H5支付
     */
    H5_PAYMENT_SUPPORT("H5_PAYMENT_SUPPORT", "支持H5支付"),

    /**
     * 支持小程序支付
     */
    MINI_APP_PAYMENT_SUPPORT("MINI_APP_PAYMENT_SUPPORT", "支持小程序支付"),

    /**
     * 支持扫码支付
     */
    QR_CODE_PAYMENT_SUPPORT("QR_CODE_PAYMENT_SUPPORT", "支持扫码支付"),

    /**
     * 支持APP支付
     */
    APP_PAYMENT_SUPPORT("APP_PAYMENT_SUPPORT", "支持APP支付");

    /**
     * 特性代码
     */
    private final String code;

    /**
     * 特性描述
     */
    private final String description;

    /**
     * 根据代码获取特性枚举
     * 
     * @param code 特性代码
     * @return FeatureType
     */
    public static FeatureType fromCode(String code) {
        for (FeatureType feature : FeatureType.values()) {
            if (feature.getCode().equals(code)) {
                return feature;
            }
        }
        return null;
    }
}
