package net.summerfarm.payment.trade.common.crypto;

import cn.hutool.crypto.symmetric.SM4;

import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Random;

/**
 * SM4对称加密工具类
 */
public class SM4Utils {

    private static final int DEFAULT_KEY_LENGTH = 16;

    private static final String ALL_CHAR = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final String ENCRYPTION_ALGORITHM = "SM4";

    private static final String MODE_CBC = "CBC";

    private static final String PKCS7PADDING = "PKCS7Padding";

    //固定值，不要修改；
    private static final IvParameterSpec sm4IvSpec = new IvParameterSpec(Base64.getDecoder().decode(
            "T172oxqWwkr8wqB9D7aR7g=="));

    private SM4Utils() {
    }

    public static String generateRandomKey() {
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < DEFAULT_KEY_LENGTH; i++) {
            sb.append(ALL_CHAR.charAt(random.nextInt(ALL_CHAR.length())));
        }
        return sb.toString();
    }

    public static String decryptBase64(String message, String sm4Key) {
        return sm4DecryptBase64(message, toSecretKeySpec(sm4Key));
    }

    public static String encryptBase64(String message, String sm4Key) {
        return sm4EncryptBase64(message, toSecretKeySpec(sm4Key));
    }

    public static SecretKeySpec toSecretKeySpec(String sm4Key) {
        return new SecretKeySpec(sm4Key.getBytes(StandardCharsets.UTF_8), ENCRYPTION_ALGORITHM);
    }

    public static String sm4DecryptBase64(String message, SecretKeySpec keySpec) {
        byte[] bytes = Base64.getDecoder().decode(message);
        byte[] decrypted = SM4Utils.decrypt(keySpec, sm4IvSpec, bytes);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    public static String sm4EncryptBase64(String message, SecretKeySpec keySpec) {
        return encryptBase64(keySpec, sm4IvSpec, message.getBytes(StandardCharsets.UTF_8));
    }


    public static String encryptBase64(SecretKey key, IvParameterSpec iv, byte[] data) {
        byte[] bytes = encrypt(key, iv, data);
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static byte[] encrypt(SecretKey key, IvParameterSpec iv, byte[] data) {
        SM4 sm4 = new SM4(MODE_CBC, PKCS7PADDING, key, iv);
        return sm4.encrypt(data);
    }

    public static byte[] decrypt(SecretKey key, IvParameterSpec iv, byte[] data) {
        SM4 sm4 = new SM4(MODE_CBC, PKCS7PADDING, key, iv);
        return sm4.decrypt(data);
    }
}
