package net.summerfarm.payment.trade.service;

import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;

/**
 * 统一支付客户端服务接口
 * 负责所有主动发起的交易操作
 */
public interface PaymentClientService {

    UnifiedPaymentResult pay(UnifiedPaymentRequest request);

    UnifiedQueryPaymentResult queryPayment(UnifiedQueryPaymentRequest request);

    UnifiedClosePaymentResult closePayment(UnifiedClosePaymentRequest request);

    UnifiedRefundResult refund(UnifiedRefundRequest request);

    UnifiedQueryRefundResult queryRefund(UnifiedQueryRefundRequest request);
}