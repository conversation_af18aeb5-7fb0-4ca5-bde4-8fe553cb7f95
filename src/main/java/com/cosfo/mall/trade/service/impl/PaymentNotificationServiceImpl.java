package com.cosfo.mall.trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayNotificationHandler;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.response.NotificationResult;
import net.summerfarm.payment.trade.service.PaymentNotificationService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支付通知服务实现
 */
@Service
@Slf4j
public class PaymentNotificationServiceImpl implements PaymentNotificationService {

    // 注册表，用于存放不同渠道的通知处理器
    private final Map<String, DinPayNotificationHandler> notificationHandlers = new ConcurrentHashMap<>();

    // 构造函数，用于注入或注册处理器
    public PaymentNotificationServiceImpl() {
        // 示例：注册智付通知处理器
        // 实际应用中，这里可能通过Spring的@Autowired或配置类进行动态注册
        notificationHandlers.put("DINPAY", new DinPayNotificationHandler());
    }

    @Override
    public NotificationResult handleNotification(String channelCode, String rawNotificationData, ChannelConfig config) {
        log.info("开始处理渠道通知: channelCode={}, rawData={}", channelCode, rawNotificationData);

        if (channelCode == null || channelCode.trim().isEmpty()) {
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(ErrorCode.INVALID_PARAMETER, "渠道标识符不能为空"), "FAIL");
        }
        if (rawNotificationData == null || rawNotificationData.trim().isEmpty()) {
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(ErrorCode.INVALID_PARAMETER, "原始通知数据不能为空"), "FAIL");
        }
        if (config == null) {
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空"), "FAIL");
        }

        DinPayNotificationHandler handler = notificationHandlers.get(channelCode);
        if (handler == null) {
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(ErrorCode.CHANNEL_NOT_SUPPORTED, "不支持的通知渠道: " + channelCode), "FAIL");
        }

        try {
            Object parsedData = handler.verifyAndParse(rawNotificationData, config);
            log.info("通知处理成功: channelCode={}, parsedData={}", channelCode, parsedData);
            return NotificationResult.success(rawNotificationData, parsedData, handler.getSuccessResponse());
        } catch (PaymentException e) {
            log.error("通知处理失败: channelCode={}, errorCode={}, message={}",
                    channelCode, e.getErrorCode(), e.getDetailMessage(), e);
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(e.getErrorCode(), e.getDetailMessage()), handler.getFailureResponse());
        } catch (Exception e) {
            log.error("通知处理异常: channelCode={}, error={}", channelCode, e.getMessage(), e);
            return NotificationResult.failed(rawNotificationData, UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage()), "FAIL");
        }
    }
}
