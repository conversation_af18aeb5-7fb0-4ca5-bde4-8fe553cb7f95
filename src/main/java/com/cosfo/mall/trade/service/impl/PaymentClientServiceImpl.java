package net.summerfarm.payment.trade.service.impl;

import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.request.*;
import net.summerfarm.payment.trade.model.response.*;
import net.summerfarm.payment.trade.service.PaymentClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一支付客户端服务实现
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Service
public class PaymentClientServiceImpl implements PaymentClientService {

    private static final Logger log = LoggerFactory.getLogger(PaymentClientServiceImpl.class);

    private final Map<String, ChannelAdapter<?, ?>> adapterRegistry = new ConcurrentHashMap<>();
    private final Map<String, ChannelClient<?, ?>> clientRegistry = new ConcurrentHashMap<>();

    public void registerAdapter(ChannelAdapter<?, ?> adapter) {
        if (adapter == null) {
            throw new IllegalArgumentException("渠道适配器不能为空");
        }
        String channelCode = adapter.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }
        adapterRegistry.put(channelCode, adapter);
        log.info("注册渠道适配器成功: channelCode={}, channelName={}, supportedFeatures={}",
                channelCode, adapter.getChannelName(), adapter.getSupportedFeatures());
    }

    public void registerClient(ChannelClient<?, ?> client) {
        if (client == null) {
            throw new IllegalArgumentException("渠道客户端不能为空");
        }
        String channelCode = client.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }
        clientRegistry.put(channelCode, client);
        log.info("注册渠道客户端成功: channelCode={}, channelName={}",
                channelCode, client.getChannelName());
    }

    @Override
    public UnifiedPaymentResult pay(UnifiedPaymentRequest request) {
        try {
            validatePaymentRequest(request);
            log.info("开始处理支付请求: paymentNo={}, totalAmount={}, paymentMethod={}",
                    request.getPaymentNo(), request.getTotalAmount(), request.getPaymentMethod());
            return processPayment(request);
        } catch (PaymentException e) {
            log.error("支付请求处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage()),
                    request.getChannelConfig() != null ? request.getChannelConfig().getChannelName() : "Unknown"
            );
        } catch (Exception e) {
            log.error("支付请求处理异常: paymentNo={}", request.getPaymentNo(), e);
            return UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage()),
                    request.getChannelConfig() != null ? request.getChannelConfig().getChannelName() : "Unknown"
            );
        }
    }

    @Override
    public UnifiedQueryPaymentResult queryPayment(UnifiedQueryPaymentRequest request) {
        try {
            validateQueryRequest(request);
            log.info("开始处理支付查询请求: paymentNo={}", request.getPaymentNo());
            return processQuery(request);
        } catch (PaymentException e) {
            log.error("支付查询处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedQueryPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("支付查询处理异常: paymentNo={}", request.getPaymentNo(), e);
            return UnifiedQueryPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public UnifiedClosePaymentResult closePayment(UnifiedClosePaymentRequest request) {
        try {
            validateCloseRequest(request);
            log.info("开始处理关单请求: paymentNo={}", request.getPaymentNo());
            return processClose(request);
        } catch (PaymentException e) {
            log.error("关单处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedClosePaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("关单处理异常: paymentNo={}", request.getPaymentNo(), e);
            return UnifiedClosePaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public UnifiedRefundResult refund(UnifiedRefundRequest request) {
        try {
            validateRefundRequest(request);
            log.info("开始处理退款请求: refundNo={}", request.getRefundNo());
            return processRefund(request);
        } catch (PaymentException e) {
            log.error("退款处理失败: refundNo={}, errorCode={}, message={}",
                    request.getRefundNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("退款处理异常: refundNo={}", request.getRefundNo(), e);
            return UnifiedRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public UnifiedQueryRefundResult queryRefund(UnifiedQueryRefundRequest request) {
        try {
            validateQueryRefundRequest(request);
            log.info("开始处理退款查询请求: refundNo={}", request.getRefundNo());
            return processQueryRefund(request);
        } catch (PaymentException e) {
            log.error("退款查询处理失败: refundNo={}, errorCode={}, message={}",
                    request.getRefundNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedQueryRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("退款查询处理异常: refundNo={}", request.getRefundNo(), e);
            return UnifiedQueryRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private <Req, Resp> UnifiedPaymentResult processPayment(UnifiedPaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);
        validateFeatureSupport(request, adapter);
        Req channelRequest = adapter.convertPaymentRequest(request);
        Resp channelResponse = client.pay(channelRequest);
        UnifiedPaymentResult result = adapter.convertPaymentResponse(channelResponse, request);
        log.info("支付请求处理完成: paymentNo={}, status={}, channelTransactionId={}",
                request.getPaymentNo(), result.getStatus(), result.getChannelTransactionId());
        return result;
    }

    private <Req, Resp> UnifiedQueryPaymentResult processQuery(UnifiedQueryPaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);
        Req channelRequest = adapter.convertQueryPaymentRequest(request);
        Resp channelResponse = client.queryPayment(channelRequest);
        UnifiedQueryPaymentResult result = adapter.convertQueryPaymentResponse(channelResponse, request);
        log.info("支付查询处理完成: paymentNo={}, status={}",
                request.getPaymentNo(), result.getStatus());
        return result;
    }

    private <Req, Resp> UnifiedClosePaymentResult processClose(UnifiedClosePaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);
        Req channelRequest = adapter.convertClosePaymentRequest(request);
        Resp channelResponse = client.closePayment(channelRequest);
        UnifiedClosePaymentResult result = adapter.convertClosePaymentResponse(channelResponse, request);
        log.info("关单处理完成: paymentNo={}, success={}",
                request.getPaymentNo(), result.isSuccess());
        return result;
    }

    private <Req, Resp> UnifiedRefundResult processRefund(UnifiedRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);
        Req channelRequest = adapter.convertRefundRequest(request);
        Resp channelResponse = client.refund(channelRequest);
        UnifiedRefundResult result = adapter.convertRefundResponse(channelResponse, request);
        log.info("退款处理完成: refundNo={}, status={}",
                request.getRefundNo(), result.getStatus());
        return result;
    }

    private <Req, Resp> UnifiedQueryRefundResult processQueryRefund(UnifiedQueryRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);
        Req channelRequest = adapter.convertQueryRefundRequest(request);
        Resp channelResponse = client.queryRefund(channelRequest);
        UnifiedQueryRefundResult result = adapter.convertQueryRefundResponse(channelResponse, request);
        log.info("退款查询处理完成: refundNo={}, status={}",
                request.getRefundNo(), result.getStatus());
        return result;
    }

    private void validateRefundRequest(UnifiedRefundRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款请求不能为空");
        }
        if (request.getRefundNo() == null || request.getRefundNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款订单号不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "原支付订单号不能为空");
        }
        if (request.getRefundAmount() == null || request.getRefundAmount() <= 0) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款金额必须大于0");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateQueryRefundRequest(UnifiedQueryRefundRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款查询请求不能为空");
        }
        if (request.getRefundNo() == null || request.getRefundNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateCloseRequest(UnifiedClosePaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "关单请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateQueryRequest(UnifiedQueryPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "查询请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validatePaymentRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付金额必须大于0");
        }
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商品描述不能为空");
        }
        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付方式不能为空");
        }
        if (request.getPlatform() == null || request.getPlatform().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "应用平台不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getChannelConfig().getChannelCode() == null ||
            request.getChannelConfig().getChannelCode().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道标识符不能为空");
        }
        if (request.getChannelConfig().getChannelName() == null ||
            request.getChannelConfig().getChannelName().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道名称不能为空");
        }
        if (request.getPayer() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者信息不能为空");
        }
        if (request.getPayer().getUserId() == null || request.getPayer().getUserId().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者用户ID不能为空");
        }
    }

    @SuppressWarnings("unchecked")
    private <Req, Resp> ChannelAdapter<Req, Resp> getChannelAdapter(String channelCode) {
        ChannelAdapter<?, ?> adapter = adapterRegistry.get(channelCode);
        if (adapter == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "不支持的支付渠道: " + channelCode);
        }
        return (ChannelAdapter<Req, Resp>) adapter;
    }

    @SuppressWarnings("unchecked")
    private <Req, Resp> ChannelClient<Req, Resp> getChannelClient(String channelCode) {
        ChannelClient<?, ?> client = clientRegistry.get(channelCode);
        if (client == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "未找到渠道客户端: " + channelCode);
        }
        return (ChannelClient<Req, Resp>) client;
    }

    private void validateFeatureSupport(UnifiedPaymentRequest request, ChannelAdapter<?, ?> adapter) {
        if (request.getTotalAmount() != null && request.getTotalAmount() == 1) {
            if (!adapter.supportsFeature(FeatureType.ALLOW_ONE_CENT_ORDER)) {
                throw new PaymentException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED,
                        "渠道不支持一分钱订单: " + adapter.getChannelName());
            }
        }
    }
}
