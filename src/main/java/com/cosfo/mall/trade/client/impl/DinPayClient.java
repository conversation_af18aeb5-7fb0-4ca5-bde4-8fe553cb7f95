package net.summerfarm.payment.trade.client.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.*;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.common.crypto.CertUtils;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.summerfarm.payment.trade.common.crypto.SM4Utils;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.common.util.DinUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;

/**
 * 智付（DinPay）渠道客户端
 * 负责与智付API进行实际的HTTP交互，包括加密、签名和验签
 * <AUTHOR>
 */
@Slf4j
public class DinPayClient implements ChannelClient<DinPayRequestDTO, DinResponseDTO<DinPayResponseDTO>> {

    private static final String CHANNEL_CODE = "DINPAY";
    private static final String CHANNEL_NAME = "智付";
    private static final String SIGNATURE_METHOD = "SM3WITHSM2";

    private final DinPayConfig dinPayConfig;

    public DinPayClient(DinPayConfig dinPayConfig) {
        if (dinPayConfig == null) {
            throw new IllegalArgumentException("智付配置不能为空");
        }
        this.dinPayConfig = dinPayConfig;
        Security.addProvider(new BouncyCastleProvider());
    }

    @Override
    public String getChannelCode() {
        return CHANNEL_CODE;
    }

    @Override
    public String getChannelName() {
        return CHANNEL_NAME;
    }

    @Override
    public DinResponseDTO<DinPayResponseDTO> pay(DinPayRequestDTO channelRequest) throws Exception {
        log.info("执行智付支付请求，订单号：{}", channelRequest.getOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getPayUrl());
        return dinCommonRequest(url, channelRequest, DinPayResponseDTO.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R queryPayment(T channelQueryRequest) throws Exception {
        if (!(channelQueryRequest instanceof DinPayQueryRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinPayQueryRequestDTO类型");
        }
        DinPayQueryRequestDTO request = (DinPayQueryRequestDTO) channelQueryRequest;
        log.info("执行智付支付查询，订单号：{}", request.getOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getQueryUrl());
        DinResponseDTO<DinPayQueryResponseDTO> response = dinCommonRequest(url, request, DinPayQueryResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R closePayment(T channelCloseRequest) throws Exception {
        if (!(channelCloseRequest instanceof DinPayCloseRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinPayCloseRequestDTO类型");
        }
        DinPayCloseRequestDTO request = (DinPayCloseRequestDTO) channelCloseRequest;
        log.info("执行智付关闭支付，订单号：{}", request.getPayOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getCloseUrl());
        DinResponseDTO<DinPayCloseResponseDTO> response = dinCommonRequest(url, request, DinPayCloseResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R refund(T channelRefundRequest) throws Exception {
        if (!(channelRefundRequest instanceof DinRefundRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinRefundRequestDTO类型");
        }
        DinRefundRequestDTO request = (DinRefundRequestDTO) channelRefundRequest;
        log.info("执行智付退款，退款订单号：{}", request.getRefundOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getRefundUrl());
        DinResponseDTO<DinRefundResponseDTO> response = dinCommonRequest(url, request, DinRefundResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R queryRefund(T channelQueryRefundRequest) throws Exception {
        if (!(channelQueryRefundRequest instanceof DinRefundQueryRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinRefundQueryRequestDTO类型");
        }
        DinRefundQueryRequestDTO request = (DinRefundQueryRequestDTO) channelQueryRefundRequest;
        log.info("执行智付退款查询，退款订单号：{}", request.getRefundOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getQueryRefundUrl());
        DinResponseDTO<DinRefundQueryResponseDTO> response = dinCommonRequest(url, request, DinRefundQueryResponseDTO.class);
        return (R) response;
    }

    private <T, R> DinResponseDTO<R> dinCommonRequest(String url, T requestData, Class<R> responseClass) {
        PrivateKey merchantPrivateKey = CertUtils.getPrivateKeyByBase64(dinPayConfig.getPrivateKey());
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(dinPayConfig.getPublicKey());
        String encryptionKey = dinPayConfig.getSecret();
        String encryptedEncryptionKey = SM2Utils.encryptToBase64(platformPublicKey, encryptionKey);
        String requestJson = JSON.toJSONString(requestData);
        log.debug("智付业务请求参数：{}", requestJson);
        String encryptedData = SM4Utils.encryptBase64(requestJson, encryptionKey);
        String signature = SM2Utils.sign(merchantPrivateKey, encryptedData);
        DinRequestDTO dinRequest = new DinRequestDTO(dinPayConfig.getMerchantNo(), encryptedEncryptionKey, SIGNATURE_METHOD, signature, String.format("%014d", System.currentTimeMillis()), encryptedData);
        String requestBody = JSON.toJSONString(dinRequest);
        DinResponseDTO<R> response = DinUtils.executeRequest(url, requestBody, responseClass, platformPublicKey);
        checkDinResponse(response);
        return response;
    }

    private void checkDinResponse(DinResponseDTO<?> response) {
        if (!"0000".equals(response.getCode())) {
            log.error("智付请求失败，错误码：{}，错误信息：{}", response.getCode(), response.getMsg());
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, String.format("智付渠道返回错误：错误码=%s，错误信息=%s", response.getCode(), response.getMsg()));
        }
        if (response.getData() == null) {
            log.error("智付响应数据为空，响应内容：{}", response);
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, "智付响应数据为空");
        }
    }
}