package net.summerfarm.payment.trade.client;

/**
 * 支付渠道客户端接口
 * 专注于与渠道API的具体交互，不负责参数转换
 *
 * @param <REQ> 渠道特定的请求类型
 * @param <RESP> 渠道特定的响应类型
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelClient<REQ, RESP> {

    /**
     * 获取渠道标识符
     *
     * @return 渠道标识符
     */
    String getChannelCode();

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();

    /**
     * 执行支付API调用
     *
     * @param channelRequest 渠道特定的支付请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    RESP pay(REQ channelRequest) throws Exception;

    /**
     * 执行支付查询API调用
     *
     * @param channelQueryRequest 渠道特定的支付查询请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default <T, R> R queryPayment(T channelQueryRequest) throws Exception {
        throw new UnsupportedOperationException("queryPayment方法未实现");
    }

    /**
     * 执行关单API调用
     *
     * @param channelCloseRequest 渠道特定的关单请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default <T, R> R closePayment(T channelCloseRequest) throws Exception {
        throw new UnsupportedOperationException("closePayment方法未实现");
    }

    /**
     * 执行退款API调用
     *
     * @param channelRefundRequest 渠道特定的退款请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default <T, R> R refund(T channelRefundRequest) throws Exception {
        throw new UnsupportedOperationException("refund方法未实现");
    }

    /**
     * 执行退款查询API调用
     *
     * @param channelQueryRefundRequest 渠道特定的退款查询请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default <T, R> R queryRefund(T channelQueryRefundRequest) throws Exception {
        throw new UnsupportedOperationException("queryRefund方法未实现");
    }
}
