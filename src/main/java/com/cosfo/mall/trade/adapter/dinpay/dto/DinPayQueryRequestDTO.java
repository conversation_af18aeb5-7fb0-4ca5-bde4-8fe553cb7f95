package net.summerfarm.payment.trade.adapter.dinpay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * @description: 智付支付查询请求DTO
 * @author: George
 * @date: 2025-02-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayQueryRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单返回的上游订单号与”请求订单号”二选一
     */
    private String channelNumber;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
