package net.summerfarm.payment.trade.adapter.dinpay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * @description: 智付关单请求DTO
 * @author: <PERSON>
 * @date: 2025-02-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayCloseRequestDTO {

    /**
     * 订单号
     */
    private String payOrderNo;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
