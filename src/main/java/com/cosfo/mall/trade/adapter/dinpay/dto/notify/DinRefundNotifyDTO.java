package net.summerfarm.payment.trade.adapter.dinpay.dto.notify;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 智付退款通知DTO
 * @author: George
 * @date: 2025-02-17
 */
@Data
public class DinRefundNotifyDTO {

    private String interfaceName;
    private String merchantId;
    private String paymentType;
    private String paymentMethods;
    private String payOrderNo;
    private String refundOrderNo;
    private String refundChannelNumber;
    private String refundOrderStatus;
    private BigDecimal refundAmount;
    private String currency;
    private String refundOrderCompleteDate;
    private String refundNotifyDate;
    private String refundChannelOrderNum;
    private BigDecimal refundOrderAmount;
    private BigDecimal refundUserAmount;
    private BigDecimal refundFee;
    private BigDecimal refundFeeAccountAmt;
    private BigDecimal refundReceiverFee;
    private BigDecimal refundOfflineFee;
    private String refundMarketingRules;
    private String refundSplitRules;
    private String refundPromotionDetail;
    private String refundDesc;
    private String retReasonDesc;
    private String upAddData;
    private String controlType;
}
