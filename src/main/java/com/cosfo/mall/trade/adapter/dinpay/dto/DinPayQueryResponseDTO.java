package com.cosfo.mall.trade.adapter.dinpay.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 支付查询响应DTO
 * @author: George
 * @date: 2025-02-12
 */
@Data
public class DinPayQueryResponseDTO {

    private String interfaceName;
    private String paymentType;
    private String paymentMethods;
    private String merchantId;
    private String orderNo;
    private BigDecimal payAmount;
    private String currency;
    private String orderStatus;
    private String refundStatus;
    private String orderDesc;
    private String channelNumber;
    private String outTransactionOrderId;
    private String subMerchantNo;
    private String appid;
    private String openid;
    private String subopenid;
    private String bankType;
    private String onlineCardType;
    private BigDecimal cashFee;
    private BigDecimal couponFee;
    private BigDecimal orderCreditAmount;
    private BigDecimal paymentAmount;
    private BigDecimal channelSettlementAmount;
    private BigDecimal orderFee;
    private BigDecimal merchantCreditAmount;
    private String chargeFlag;
    private BigDecimal merchantFee;
    private BigDecimal feeAccountAmt;
    private BigDecimal receiverFee;
    private BigDecimal offlineFee;
    private String orderPayDate;
    private String marketingRules;
    private String splitRules;
    private String wxTradeType;
    private String upAddData;
    private String resvData;
    private String fundBillList;
    private String promotionDetail;
    private List<VoucherDetail> voucherDetailList;
    private String controlType;
    private String controlStatus;

    @Data
    public static class VoucherDetail {
        private String amount;
        private String merchantContribute;
        private String name;
        private String otherContribute;
        private String type;
        private String voucherId;
    }
}
