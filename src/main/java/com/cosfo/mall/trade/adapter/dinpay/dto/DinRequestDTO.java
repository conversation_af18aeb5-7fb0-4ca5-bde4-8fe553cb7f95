package com.cosfo.mall.trade.adapter.dinpay.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-08
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinRequestDTO {

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 密钥
     */
    private String encryptionKey;

    /**
     * 签名方式
     */
    private String signatureMethod;

    /**
     * 签名
     */
    private String sign;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 数据
     */
    private String data;
}
