package com.cosfo.mall.trade.adapter;

import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;

import java.util.Set;

/**
 * 支付渠道适配器接口
 * 专注于参数转换和特性发现，不负责具体的API调用
 *
 * @param <REQ> 渠道特定的请求类型
 * @param <RESP> 渠道特定的响应类型
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelAdapter<REQ, RESP> {

    String getChannelCode();

    String getChannelName();

    Set<FeatureType> getSupportedFeatures();

    REQ convertPaymentRequest(UnifiedPaymentRequest request);

    UnifiedPaymentResult convertPaymentResponse(RESP channelResponse, UnifiedPaymentRequest originalRequest);

    default <T> T convertQueryPaymentRequest(UnifiedQueryPaymentRequest request) {
        throw new UnsupportedOperationException("convertQueryPaymentRequest方法未实现");
    }

    default <R> UnifiedQueryPaymentResult convertQueryPaymentResponse(R channelQueryResponse, UnifiedQueryPaymentRequest originalRequest) {
        throw new UnsupportedOperationException("convertQueryPaymentResponse方法未实现");
    }

    default <T> T convertClosePaymentRequest(UnifiedClosePaymentRequest request) {
        throw new UnsupportedOperationException("convertClosePaymentRequest方法未实现");
    }

    default <R> UnifiedClosePaymentResult convertClosePaymentResponse(R channelCloseResponse, UnifiedClosePaymentRequest originalRequest) {
        throw new UnsupportedOperationException("convertClosePaymentResponse方法未实现");
    }

    default <T> T convertRefundRequest(UnifiedRefundRequest request) {
        throw new UnsupportedOperationException("convertRefundRequest方法未实现");
    }

    default <R> UnifiedRefundResult convertRefundResponse(R channelRefundResponse, UnifiedRefundRequest originalRequest) {
        throw new UnsupportedOperationException("convertRefundResponse方法未实现");
    }

    default <T> T convertQueryRefundRequest(UnifiedQueryRefundRequest request) {
        throw new UnsupportedOperationException("convertQueryRefundRequest方法未实现");
    }

    default <R> UnifiedQueryRefundResult convertQueryRefundResponse(R channelQueryRefundResponse, UnifiedQueryRefundRequest originalRequest) {
        throw new UnsupportedOperationException("convertQueryRefundResponse方法未实现");
    }

    default boolean supportsFeature(FeatureType feature) {
        Set<FeatureType> supportedFeatures = getSupportedFeatures();
        return supportedFeatures != null && supportedFeatures.contains(feature);
    }

    default void validateRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("支付请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new IllegalArgumentException("支付金额必须大于0");
        }
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("商品描述不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("渠道配置不能为空");
        }
    }
}
