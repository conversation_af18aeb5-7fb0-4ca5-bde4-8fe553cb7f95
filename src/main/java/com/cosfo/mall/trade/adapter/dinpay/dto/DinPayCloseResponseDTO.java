package net.summerfarm.payment.trade.adapter.dinpay.dto;

import lombok.Data;

/**
 * @description: 智付关单响应DTO
 * @author: <PERSON>
 * @date: 2025-02-12
 */
@Data
public class DinPayCloseResponseDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 订单号
     */
    private String payOrderNo;

    /**
     * 上游交易请求订单号
     */
    private String payChannelNumber;
}
