package com.cosfo.mall.trade.adapter.dinpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;
import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.*;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.common.enums.RefundStatus;
import net.summerfarm.payment.trade.model.common.PaymentCredential;
import net.summerfarm.payment.trade.model.request.*;
import net.summerfarm.payment.trade.model.response.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 智付（DinPay）渠道适配器
 * 负责将SDK的统一模型与智付的特定模型进行互相转换
 * <AUTHOR>
 */
public class DinPayAdapter implements ChannelAdapter<DinPayRequestDTO, DinResponseDTO<DinPayResponseDTO>> {

    @Override
    public String getChannelCode() {
        return PaymentChannelProviderEnums.DIN_PAY.name();
    }

    @Override
    public String getChannelName() {
        return PaymentChannelProviderEnums.DIN_PAY.getChannelName();
    }

    @Override
    public Set<FeatureType> getSupportedFeatures() {
        return Collections.emptySet();
    }

    @Override
    public DinPayRequestDTO convertPaymentRequest(UnifiedPaymentRequest request) {
        if (request.getChannelConfig() == null || request.getChannelConfig().getExtraConfig() == null) {
            throw new IllegalArgumentException("智付渠道扩展配置不能为空");
        }
        net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig dinPayConfig = JSON.parseObject(JSON.toJSONString(request.getChannelConfig().getExtraConfig()), net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig.class);
        String interfaceName = determineInterfaceName(request.getPlatform());
        String paymentMethods = determinePaymentMethods(request.getPlatform());
        String appId = determineAppId(request.getPlatform(), dinPayConfig);
        String openId = request.getPayer().getUserId();
        BigDecimal payAmountInYuan = new BigDecimal(request.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        String payType = Objects.equals(request.getPaymentMethod(), PaymentMethodEnums.WECHAT.getCode()) ? DinPaymentEnum.paymentType.WECHAT.getType() : DinPaymentEnum.paymentType.ALIPAY.getType();

        return DinPayRequestDTO.builder()
                .interfaceName(interfaceName)
                .orderNo(request.getPaymentNo())
                .paymentType(payType)
                .paymentMethods(paymentMethods)
                .appid(appId)
                .openId(openId)
                .payAmount(payAmountInYuan)
                .currency(request.getCurrency())
                .orderIp(request.getPayer().getIp())
                .goodsName(request.getDescription())
                .notifyUrl(request.getNotifyUrl())
                .timeExpire(request.getTimeExpireSeconds())
                .orderDesc(request.getDescription())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    @Override
    public UnifiedPaymentResult convertPaymentResponse(DinResponseDTO<DinPayResponseDTO> channelResponse, UnifiedPaymentRequest originalRequest) {
        DinPayResponseDTO dinResponseData = channelResponse.getData();
        String payInfo = dinResponseData.getPayInfo();
        JSONObject jsonObject = JSONObject.parseObject(payInfo);
        Map<String, Object> extraData = new java.util.HashMap<>();
        extraData.put("appId", dinResponseData.getAppid());
        extraData.put("timeStamp", jsonObject.getString("timeStamp"));
        extraData.put("nonceStr", jsonObject.getString("nonceStr"));
        extraData.put("package", jsonObject.getString("package"));
        extraData.put("signType", jsonObject.getString("signType"));
        extraData.put("paySign", jsonObject.getString("paySign"));

        PaymentCredential credential = PaymentCredential.builder()
                .content(payInfo)
                .extraData(extraData)
                .build();

        return UnifiedPaymentResult.builder()
                .status(PaymentStatus.PENDING)
                .paymentNo(originalRequest.getPaymentNo())
                .channelTransactionId(dinResponseData.getOutTransactionOrderId())
                .credential(credential)
                .rawResponse(JSON.toJSONString(channelResponse))
                .timestamp(System.currentTimeMillis())
                .build();
    }

    @Override
    public DinPayQueryRequestDTO convertQueryPaymentRequest(UnifiedQueryPaymentRequest request) {
        return DinPayQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_QUERY.getName())
                .orderNo(request.getPaymentNo())
                .channelNumber(request.getChannelTransactionId())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public UnifiedQueryPaymentResult convertQueryPaymentResponse(DinResponseDTO<DinPayQueryResponseDTO> channelQueryResponse, UnifiedQueryPaymentRequest originalRequest) {
        DinPayQueryResponseDTO responseData = channelQueryResponse.getData();
        PaymentStatus status = convertToUnifiedStatus(responseData.getOrderStatus());
        return UnifiedQueryPaymentResult.builder()
                .status(status)
                .paymentNo(responseData.getOrderNo())
                .channelTransactionId(responseData.getOutTransactionOrderId())
                .rawResponse(JSON.toJSONString(channelQueryResponse))
                .build();
    }

    @Override
    public DinPayCloseRequestDTO convertClosePaymentRequest(UnifiedClosePaymentRequest request) {
        return DinPayCloseRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_CLOSE.getName())
                .payOrderNo(request.getPaymentNo())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public UnifiedClosePaymentResult convertClosePaymentResponse(DinResponseDTO<DinPayCloseResponseDTO> channelCloseResponse, UnifiedClosePaymentRequest originalRequest) {
        boolean isSuccess = DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelCloseResponse.getCode());
        return UnifiedClosePaymentResult.builder()
                .success(isSuccess)
                .paymentNo(originalRequest.getPaymentNo())
                .rawResponse(JSON.toJSONString(channelCloseResponse))
                .build();
    }

    @Override
    public DinRefundRequestDTO convertRefundRequest(UnifiedRefundRequest request) {
        BigDecimal refundAmountInYuan = new BigDecimal(request.getRefundAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return DinRefundRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND.getName())
                .payOrderNo(request.getPaymentNo())
                .refundOrderNo(request.getRefundNo())
                .refundAmount(refundAmountInYuan)
                .notifyUrl(request.getNotifyUrl())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public UnifiedRefundResult convertRefundResponse(DinResponseDTO<DinRefundResponseDTO> channelRefundResponse, UnifiedRefundRequest originalRequest) {
        DinRefundResponseDTO responseData = channelRefundResponse.getData();
        RefundStatus status = convertToUnifiedRefundStatus(responseData.getControlType());
        return UnifiedRefundResult.builder()
                .status(status)
                .refundNo(responseData.getRefundOrderNo())
                .channelRefundId(responseData.getRefundChannelNumber())
                .rawResponse(JSON.toJSONString(channelRefundResponse))
                .build();
    }

    @Override
    public DinRefundQueryRequestDTO convertQueryRefundRequest(UnifiedQueryRefundRequest request) {
        return DinRefundQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND_QUERY.getName())
                .refundOrderNo(request.getRefundNo())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public UnifiedQueryRefundResult convertQueryRefundResponse(DinResponseDTO<DinRefundQueryResponseDTO> channelQueryRefundResponse, UnifiedQueryRefundRequest originalRequest) {
        DinRefundQueryResponseDTO responseData = channelQueryRefundResponse.getData();
        RefundStatus status = convertToUnifiedRefundStatus(responseData.getOrderStatus());
        return UnifiedQueryRefundResult.builder()
                .status(status)
                .refundNo(responseData.getRefundOrderNo())
                .channelRefundId(responseData.getRefundChannelNumber())
                .rawResponse(JSON.toJSONString(channelQueryRefundResponse))
                .build();
    }

    private RefundStatus convertToUnifiedRefundStatus(String dinPayRefundStatus) {
        if (dinPayRefundStatus == null) return RefundStatus.UNKNOWN;

        // 使用DinPaymentEnum中的退款状态枚举进行匹配
        if (DinPaymentEnum.refundStatus.SUCCESS.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.SUCCESS;
        }
        if (DinPaymentEnum.refundStatus.FAIL.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.FAILED;
        }
        if (DinPaymentEnum.refundStatus.DOING.getStatus().equals(dinPayRefundStatus) ||
            DinPaymentEnum.refundStatus.RECEIVE.getStatus().equals(dinPayRefundStatus) ||
            DinPaymentEnum.refundStatus.BEFORE_RECEIVE.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.PENDING;
        }
        if (DinPaymentEnum.refundStatus.CLOSE.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.CLOSED;
        }
        return RefundStatus.UNKNOWN;
    }

    private PaymentStatus convertToUnifiedStatus(String dinPayStatus) {
        if (dinPayStatus == null) return PaymentStatus.UNKNOWN;

        // 使用DinPaymentEnum中的订单状态枚举进行匹配
        if (DinPaymentEnum.orderStatus.SUCCESS.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.SUCCESS;
        }
        if (DinPaymentEnum.orderStatus.FAIL.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.FAILED;
        }
        if (DinPaymentEnum.orderStatus.INIT.getStatus().equals(dinPayStatus) ||
            DinPaymentEnum.orderStatus.DOING.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.PENDING;
        }
        if (DinPaymentEnum.orderStatus.CLOSE.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.CLOSED;
        }
        if (DinPaymentEnum.orderStatus.CANCEL.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.CANCELLED;
        }
        return PaymentStatus.UNKNOWN;
    }

    private String determineInterfaceName(String platform) {
        if (PaymentDictionaryEnums.Platform.MINI_APP.getName().equalsIgnoreCase(platform)) {
            return DinPaymentEnum.interfaceName.MINI_APP.getName();
        }
        // 默认返回公众号支付接口名称
        return DinPaymentEnum.interfaceName.PUBLIC_ACCOUNT.getName();
    }

    private String determinePaymentMethods(String platform) {
        if (PaymentDictionaryEnums.Platform.MINI_APP.getName().equalsIgnoreCase(platform)) {
            return DinPaymentEnum.paymentMethods.MINI_APP.getMethod();
        }
        return DinPaymentEnum.paymentMethods.PUBLIC.getMethod();
    }

    private String determineAppId(String platform, DinPayConfig config) {
        if (PaymentDictionaryEnums.Platform.MINI_APP.getName().equalsIgnoreCase(platform)) {
            return config.getMiniAppId();
        }
        return config.getAppId();
    }
}
