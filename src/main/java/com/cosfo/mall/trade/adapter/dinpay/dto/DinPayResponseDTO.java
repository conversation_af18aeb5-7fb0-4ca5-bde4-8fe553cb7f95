package com.cosfo.mall.trade.adapter.dinpay.dto;

import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-11
 **/
@Data
public class DinPayResponseDTO {
    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 支付客户端类型
     */
    private String paymentType;

    /**
     * 支付方式
     */
    private String paymentMethods;

    /**
     * 商户编号
     */
    private String merchantId;

    /**
     * 请求订单号
     */
    private String orderNo;

    /**
     * 交易金额
     */
    private Double payAmount;

    /**
     * 币种类型
     */
    private String currency;

    /**
     * 公众账号 ID
     */
    private String appid;

    /**
     * js 支付信息
     */
    private String payInfo;

    /**
     * 上游订单号
     */
    private String outTransactionOrderId;

    /**
     * 上游返回码
     */
    private String channelRetCode;

    /**
     * 渠道子商户号(U/A/T)
     */
    private String subMerchantNo;

    /**
     * 管控类型
     */
    private String controlType;

    /**
     * 管控状态
     */
    private String controlStatus;
}
