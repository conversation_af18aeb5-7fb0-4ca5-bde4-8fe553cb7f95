package com.cosfo.mall.trade.adapter.dinpay;

import lombok.Data;

/**
 * @description: 智付支付配置
 * @author: George
 * @date: 2025-01-07
 */
@Data
public class DinPayConfig {

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 加密密钥
     */
    private String secret;

    /**
     * 平台公钥 (Base64)
     */
    private String publicKey;

    /**
     * 商户私钥 (Base64)
     */
    private String privateKey;

    /**
     * 智付网关域名
     */
    private String domain;

    /**
     * 支付请求URL路径
     */
    private String payUrl;

    /**
     * 支付查询请求URL路径
     */
    private String queryUrl;

    /**
     * 关单请求URL路径
     */
    private String closeUrl;

    /**
     * 退款请求URL路径
     */
    private String refundUrl;

    /**
     * 退款查询请求URL路径
     */
    private String queryRefundUrl;

    /**
     * 小程序AppID
     */
    private String miniAppId;

    /**
     * 公众号/H5 AppID
     */
    private String appId;
}
