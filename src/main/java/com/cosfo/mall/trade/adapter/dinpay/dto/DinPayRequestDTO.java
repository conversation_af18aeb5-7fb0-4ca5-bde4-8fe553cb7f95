package net.summerfarm.payment.trade.adapter.dinpay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2025-01-08
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 支付方法
     */
    private String paymentMethods;

    /**
     * appId
     */
    private String appid;

    /**
     * openId
     */
    @JSONField(name = "openid")
    private String openId;

    /**
     * 支付金额
     */
    private java.math.BigDecimal payAmount;

    /**
     * 货币
     */
    private String currency;

    /***
     * ip
     */
    private String orderIp;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 通知地址
     */
    private String notifyUrl;

    /**
     * 交易过期时间(单位:秒)
     */
    private String timeExpire;

    /**
     * 订单备注信息，商户可自定义上送，原样返回
     */
    private String orderDesc;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
