package com.cosfo.mall.trade.adapter.dinpay.dto.notify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description: 智付支付通知DTO
 * @author: George
 * @date: 2025-02-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayNotifyDTO {

    private String paymentType;
    private String paymentMethods;
    private String merchantId;
    private String orderNo;
    private BigDecimal payAmount;
    private String currency;
    private String orderStatus;
    private String refundStatus;
    private String orderDesc;
    private String channelNumber;
    private String outTransactionOrderId;
    private String subMerchantNo;
    private String appid;
    private String openid;
    private String subopenid;
    private String bankType;
    private String onlineCardType;
    private BigDecimal cashFee;
    private BigDecimal couponFee;
    private BigDecimal orderCreditAmount;
    private BigDecimal paymentAmount;
    private BigDecimal channelSettlementAmount;
    private BigDecimal orderFee;
    private BigDecimal merchantCreditAmount;
    private String chargeFlag;
    private BigDecimal merchantFee;
    private BigDecimal feeAccountAmt;
    private BigDecimal receiverFee;
    private BigDecimal offlineFee;
    private String orderPayDate;
    private String timestamp;
    private String marketingRules;
    private String splitRules;
    private String wxTradeType;
    private String upAddData;
    private String resvData;
    private String fundBillList;
    private String promotionDetail;
    private String voucherDetailList;
    private String controlType;
    private String controlStatus;
}
