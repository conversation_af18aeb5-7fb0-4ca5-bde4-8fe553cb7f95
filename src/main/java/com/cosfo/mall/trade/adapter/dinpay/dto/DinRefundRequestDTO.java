package net.summerfarm.payment.trade.adapter.dinpay.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description: 智付退款请求DTO
 * @author: George
 * @date: 2025-02-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinRefundRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 原交易支付单号
     */
    private String payOrderNo;

    /**
     * 退款单号
     */
    private String refundOrderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 回调url
     */
    private String notifyUrl;

}
