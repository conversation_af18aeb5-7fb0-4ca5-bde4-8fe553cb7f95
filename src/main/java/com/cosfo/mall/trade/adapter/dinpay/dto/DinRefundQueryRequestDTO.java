package net.summerfarm.payment.trade.adapter.dinpay.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 智付退款查询请求DTO
 * @author: <PERSON>
 * @date: 2025-02-17
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DinRefundQueryRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 退款单号
     */
    private String refundOrderNo;

}
