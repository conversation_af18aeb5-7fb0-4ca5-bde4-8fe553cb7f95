package com.cosfo.mall.trade.adapter.dinpay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * @description: 智付退款查询请求DTO
 * @author: <PERSON>
 * @date: 2025-02-17
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DinRefundQueryRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 退款单号
     */
    private String refundOrderNo;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;

}
