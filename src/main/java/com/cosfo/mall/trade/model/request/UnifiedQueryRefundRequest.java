package com.cosfo.mall.trade.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;

/**
 * 统一退款查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryRefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户退款订单号（必填）
     */
    private String refundNo;

    /**
     * 原支付订单号（可选）
     */
    private String paymentNo;

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;
}
