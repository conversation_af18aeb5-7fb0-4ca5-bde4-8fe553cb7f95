package net.summerfarm.payment.trade.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分账信息模型（暂时预留，后续实现）
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfitSharingInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账接收方列表
     */
    private List<ProfitSharingReceiver> receivers;

    /**
     * 是否延迟分账（默认false，即时分账）
     */
    private Boolean delaySharing;

    /**
     * 分账描述
     */
    private String description;

    /**
     * 分账接收方信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProfitSharingReceiver implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 接收方类型（MERCHANT_ID: 商户号, PERSONAL_OPENID: 个人openid等）
         */
        private String type;

        /**
         * 接收方账号
         */
        private String account;

        /**
         * 接收方姓名/名称
         */
        private String name;

        /**
         * 分账金额（单位：元）
         */
        private java.math.BigDecimal amount;

        /**
         * 分账描述
         */
        private String description;
    }
}
