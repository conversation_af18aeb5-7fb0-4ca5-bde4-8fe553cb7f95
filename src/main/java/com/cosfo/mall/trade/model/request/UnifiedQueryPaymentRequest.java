package net.summerfarm.payment.trade.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;

/**
 * 统一支付查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryPaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户支付订单号（必填）
     * 与发起支付时使用的商户订单号相同
     */
    private String paymentNo;

    /**
     * 渠道返回的交易流水号/订单号（可选）
     * 如果提供，优先使用此字段进行查询
     */
    private String channelTransactionId;

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;
}
