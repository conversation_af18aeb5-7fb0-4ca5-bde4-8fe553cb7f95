package net.summerfarm.payment.trade.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;

/**
 * 统一关单请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedClosePaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户支付订单号（必填）
     */
    private String paymentNo;

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;
}
