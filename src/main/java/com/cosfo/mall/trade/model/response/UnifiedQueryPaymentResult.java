package net.summerfarm.payment.trade.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 统一支付查询结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryPaymentResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付状态
     */
    private PaymentStatus status;

    /**
     * 商户订单号
     */
    private String paymentNo;

    /**
     * 渠道返回的交易流水号/订单号
     */
    private String channelTransactionId;

    /**
     * 渠道返回的原始报文
     */
    private String rawResponse;

    /**
     * 错误信息（失败时才有）
     */
    private UnifiedError error;

    /**
     * 创建一个失败的查询结果
     */
    public static UnifiedQueryPaymentResult failed(String paymentNo, UnifiedError error) {
        return UnifiedQueryPaymentResult.builder()
                .status(PaymentStatus.FAILED)
                .paymentNo(paymentNo)
                .error(error)
                .build();
    }
}
