package net.summerfarm.payment.trade.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 通知处理结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知是否处理成功（验签通过，数据解析成功）
     */
    private boolean success;

    /**
     * 渠道返回的原始通知数据
     */
    private String rawData;

    /**
     * 业务数据（解析后的通知内容，可以是支付通知、退款通知等）
     */
    private Object businessData;

    /**
     * 错误信息（如果处理失败）
     */
    private UnifiedError error;

    /**
     * 渠道响应给通知方的字符串（例如："SUCCESS"或"FAIL"）
     */
    private String channelResponse;

    public static NotificationResult success(String rawData, Object businessData, String channelResponse) {
        return NotificationResult.builder()
                .success(true)
                .rawData(rawData)
                .businessData(businessData)
                .channelResponse(channelResponse)
                .build();
    }

    public static NotificationResult failed(String rawData, UnifiedError error, String channelResponse) {
        return NotificationResult.builder()
                .success(false)
                .rawData(rawData)
                .error(error)
                .channelResponse(channelResponse)
                .build();
    }
}
