package net.summerfarm.payment.trade.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 统一关单结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedClosePaymentResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关单是否成功
     */
    private boolean success;

    /**
     * 商户订单号
     */
    private String paymentNo;

    /**
     * 渠道返回的原始报文
     */
    private String rawResponse;

    /**
     * 错误信息（失败时才有）
     */
    private UnifiedError error;

    /**
     * 创建一个失败的关单结果
     */
    public static UnifiedClosePaymentResult failed(String paymentNo, UnifiedError error) {
        return UnifiedClosePaymentResult.builder()
                .success(false)
                .paymentNo(paymentNo)
                .error(error)
                .build();
    }

    /**
     * 创建一个成功的关单结果
     */
    public static UnifiedClosePaymentResult success(String paymentNo, String rawResponse) {
        return UnifiedClosePaymentResult.builder()
                .success(true)
                .paymentNo(paymentNo)
                .rawResponse(rawResponse)
                .build();
    }
}
