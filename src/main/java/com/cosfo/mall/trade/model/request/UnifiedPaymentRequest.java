package com.cosfo.mall.trade.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.common.GoodsDetail;
import net.summerfarm.payment.trade.model.common.PayerInfo;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一支付请求模型
 * 参考微信支付、支付宝等主流支付平台的标准字段设计
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedPaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础订单信息 ==========

    /**
     * 商户订单号（必填）
     * 商户系统内部订单号，要求32个字符内，只能是数字、大小写字母_-|*且在同一个商户号下唯一
     */
    private String paymentNo;

    /**
     * 商品描述（必填）
     * 商品简要描述，该字段请按照规范传递
     */
    private String description;

    /**
     * 商户数据包（可选）
     * 附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用，最长128字符
     */
    private String attach;

    // ========== 金额信息 ==========

    /**
     * 订单总金额（必填）
     * 订单总金额，单位为分，只能为整数
     */
    private Integer totalAmount;

    /**
     * 货币类型（可选，默认CNY）
     * 符合ISO 4217标准的三位字母代码，人民币：CNY
     */
    private String currency;

    // ========== 时间信息 ==========

    /**
     * 交易结束时间（可选）
     * 订单失效时间，格式为yyyy-MM-dd'T'HH:mm:ss+TIMEZONE
     */
    private LocalDateTime timeExpire;

    /**
     * 交易结束秒数（可选）
     */
    private String timeExpireSeconds;

    // ========== 通知信息 ==========

    /**
     * 通知地址（必填）
     * 异步接收支付结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数
     */
    private String notifyUrl;


    // ========== 支付者信息 ==========

    /**
     * 支付者信息（必填）
     */
    private PayerInfo payer;

    /**
     * 商品信息（非必填）
     */
    private List<GoodsDetail> goodsDetail;


    // ========== 业务扩展字段 ==========

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 业务线标识（必填）
     */
    private String businessLine;

    /**
     * 支付方式（必填）
     * @see net.summerfarm.payment.routing.common.enums.PaymentMethodEnums
     */
    private String paymentMethod;

    /**
     * 应用平台（必填：miniapp, h5, app等）
     */
    private String platform;

    /**
     * 小程序/公众号载体appid
     */
    private String appId;

    /**
     * 是否是正式环境
     */
    private Boolean proEnv;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;


    /**
     * 获取币种，默认为CNY
     *
     * @return 币种
     */
    public String getCurrency() {
        return currency != null ? currency : "CNY";
    }

    /**
     * 获取扩展参数值

    /**
     * 转换金额：元转分
     *
     * @param amount 金额（元）
     * @return 金额（分）
     */
    public static Integer convertYuanToFen(BigDecimal amount) {
        if (amount == null) {
            return null;
        }
        return amount.multiply(new BigDecimal("100")).intValue();
    }

    /**
     * 转换金额：分转元
     *
     * @param amount 金额（分）
     * @return 金额（元）
     */
    public static BigDecimal convertFenToYuan(Integer amount) {
        if (amount == null) {
            return null;
        }
        return new BigDecimal(amount).divide(new BigDecimal("100"));
    }
}
