package net.summerfarm.payment.trade.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.model.common.PaymentCredential;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;
import java.util.Map;

/**
 * 统一支付响应模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedPaymentResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付状态
     */
    private PaymentStatus status;

    /**
     * 商户订单号
     */
    private String paymentNo;

    /**
     * SDK内部生成的支付单ID
     */
    private String paymentOrderId;

    /**
     * 渠道返回的交易流水号/订单号
     */
    private String channelTransactionId;

    /**
     * 支付凭证信息（如二维码、支付链接等）
     */
    private PaymentCredential credential;

    /**
     * 渠道返回的原始报文
     */
    private String rawResponse;

    /**
     * 错误信息（失败时才有）
     */
    private UnifiedError error;

    /**
     * 扩展信息
     */
    private Map<String, Object> extraInfo;

    /**
     * 处理时间戳
     */
    private Long timestamp;



    public static UnifiedPaymentResult failed(String paymentNo,
                                              UnifiedError unifiedError) {
        return UnifiedPaymentResult.builder()
                .paymentNo(paymentNo)
                .status(PaymentStatus.FAILED)
                .error(unifiedError)
                .build();

    }

    public boolean isSuccess() {
        return PaymentStatus.SUCCESS.equals(this.status);
    }



}