package com.cosfo.mall.trade.registry;

import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.client.impl.DinPayClient;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 渠道组件注册中心
 * 负责管理所有支付渠道的适配器和客户端
 *
 * <AUTHOR> Agent
 * @date 2025-08-19
 */
public class ChannelRegistry {

    private static final Logger log = LoggerFactory.getLogger(ChannelRegistry.class);

    private final Map<String, ChannelAdapter<?, ?>> adapterRegistry = new ConcurrentHashMap<>();
    private final Map<String, ChannelClient<?, ?>> clientRegistry = new ConcurrentHashMap<>();

    /**
     * 初始化所有渠道组件
     */
    public void initialize() {
        log.info("开始初始化支付渠道组件...");

        // 初始化智付渠道
        initializeDinPayChannel();

        // 验证组件匹配性
        validateChannelComponents();

        log.info("支付渠道组件初始化完成，适配器数量：{}，客户端数量：{}",
                adapterRegistry.size(), clientRegistry.size());
    }

    /**
     * 初始化智付渠道组件
     */
    private void initializeDinPayChannel() {
        try {
            // 创建智付适配器
            DinPayAdapter dinPayAdapter = new DinPayAdapter();
            registerAdapter(dinPayAdapter);

            // 创建智付客户端（无状态）
            DinPayClient dinPayClient = new DinPayClient();
            registerClient(dinPayClient);

            log.info("{}渠道组件初始化成功", PaymentChannelProviderEnums.DIN_PAY.getChannelName());
        } catch (Exception e) {
            log.error("{}渠道组件初始化失败", PaymentChannelProviderEnums.DIN_PAY.getChannelName(), e);
        }
    }

    /**
     * 注册适配器
     */
    private void registerAdapter(ChannelAdapter<?, ?> adapter) {
        if (adapter == null) {
            throw new IllegalArgumentException("渠道适配器不能为空");
        }
        String channelCode = adapter.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }
        adapterRegistry.put(channelCode, adapter);
        log.info("注册渠道适配器成功: channelCode={}, channelName={}, supportedFeatures={}",
                channelCode, adapter.getChannelName(), adapter.getSupportedFeatures());
    }

    /**
     * 注册客户端
     */
    private void registerClient(ChannelClient<?, ?> client) {
        if (client == null) {
            throw new IllegalArgumentException("渠道客户端不能为空");
        }
        String channelCode = client.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }
        clientRegistry.put(channelCode, client);
        log.info("注册渠道客户端成功: channelCode={}, channelName={}",
                channelCode, client.getChannelName());
    }



    /**
     * 获取适配器
     */
    @SuppressWarnings("unchecked")
    public <Req, Resp> ChannelAdapter<Req, Resp> getAdapter(String channelCode) {
        ChannelAdapter<?, ?> adapter = adapterRegistry.get(channelCode);
        if (adapter == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "不支持的支付渠道: " + channelCode);
        }
        return (ChannelAdapter<Req, Resp>) adapter;
    }

    /**
     * 获取客户端
     */
    @SuppressWarnings("unchecked")
    public <Req, Resp> ChannelClient<Req, Resp> getClient(String channelCode) {
        ChannelClient<?, ?> client = clientRegistry.get(channelCode);
        if (client == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "未找到渠道客户端: " + channelCode);
        }
        return (ChannelClient<Req, Resp>) client;
    }

    /**
     * 验证适配器和客户端的匹配性
     */
    private void validateChannelComponents() {
        for (String channelCode : adapterRegistry.keySet()) {
            if (!clientRegistry.containsKey(channelCode)) {
                log.warn("适配器 {} 没有对应的客户端实现", channelCode);
            }
        }

        for (String channelCode : clientRegistry.keySet()) {
            if (!adapterRegistry.containsKey(channelCode)) {
                log.warn("客户端 {} 没有对应的适配器实现", channelCode);
            }
        }
    }

    /**
     * 检查渠道是否支持
     */
    public boolean isChannelSupported(String channelCode) {
        return adapterRegistry.containsKey(channelCode) && clientRegistry.containsKey(channelCode);
    }

    /**
     * 获取所有支持的渠道代码
     */
    public java.util.Set<String> getSupportedChannels() {
        return adapterRegistry.keySet();
    }
}
