package com.cosfo.mall.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.ItemTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.bill.service.BillProfitSharingSnapshotService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.config.OrderInterceptStoreGroupConfig;
import com.cosfo.mall.common.constant.*;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.BillProfitSharingOrderStatusEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.context.TenantDeliveryFeeRuleTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.factory.PayStrategyFactory;
import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.mq.model.DelayData;
import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.common.utils.*;
import com.cosfo.mall.delivery.model.vo.FulfillmentDeliveryVO;
import com.cosfo.mall.facade.*;
import com.cosfo.mall.facade.dto.CombineItemDTO;
import com.cosfo.mall.facade.dto.CombineMarketDetailDTO;
import com.cosfo.mall.facade.dto.CombineMarketQueryInputDTO;
import com.cosfo.mall.facade.ofc.OfcDeliveryInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreGroupFacade;
import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemPriceService;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.marketing.model.ItemSaleLimitRuleEnum;
import com.cosfo.mall.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.mall.marketing.service.ItemSaleLimitConfigService;
import com.cosfo.mall.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.merchant.service.MerchantDeliveryFeeService;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.openapi.service.OrderNotifyBizService;
import com.cosfo.mall.order.converter.OrderAddressConverter;
import com.cosfo.mall.order.converter.OrderConverter;
import com.cosfo.mall.order.converter.OrderDeliveryConvert;
import com.cosfo.mall.order.converter.OrderItemConverter;
import com.cosfo.mall.order.mapper.OrderSelfLiftingMapper;
import com.cosfo.mall.order.mapper.TrolleyMapper;
import com.cosfo.mall.order.model.bo.OrderItemNonCashCalculationParamBO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationParamBO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationResultBO;
import com.cosfo.mall.order.model.dto.*;
import com.cosfo.mall.order.model.po.*;
import com.cosfo.mall.order.model.vo.*;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.order.service.OrderItemFeeCalculateService;
import com.cosfo.mall.order.service.OrderItemFeeTransactionService;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.model.vo.PaymentCombinedDetailVO;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.planorder.model.vo.UnfinishedPlanOrderVO;
import com.cosfo.mall.planorder.service.PlanOrderService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.service.ProductSkuService;
import com.cosfo.mall.stock.model.dto.OrderSelfSupplyOccupyDTO;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.system.model.po.SystemParameters;
import com.cosfo.mall.system.service.SystemParameterService;
import com.cosfo.mall.tenant.factory.DeliveryFeeStrategyFactory;
import com.cosfo.mall.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.mall.tenant.model.dto.DeliveryFeeResultDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.model.po.Tenant;
import com.cosfo.mall.tenant.model.po.TenantCommonConfig;
import com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.mall.tenant.service.*;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.manage.client.enums.AgentOrderEnum;
import com.cosfo.manage.client.planorder.PlanOrderCommandProvider;
import com.cosfo.manage.client.planorder.PlanOrderQueryProvider;
import com.cosfo.manage.client.planorder.req.CreateOrderSuccessReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.*;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.bizlog.QueryBizLogReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.ofc.client.common.message.FulfillmentInfo;
import net.summerfarm.ofc.client.common.message.cosfo.CosfoMessageDTO;
import net.summerfarm.ofc.client.common.message.cosfo.FulfillmentOrderResultMessageDTO;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetail;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.BizErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/28
 */
@Service
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Resource
    private TenantService tenantService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Lazy
    @Resource
    private StockService stockService;
    @Resource
    private TrolleyMapper trolleyMapper;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;
    @Resource
    private MerchantDeliveryFeeService merchantDeliveryFeeService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private TenantDeliveryFeeRuleService tenantDeliveryFeeRuleService;
    @Resource
    private TenantBillService tenantBillService;
    @Resource
    private AreaService areaService;
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Lazy
    @Resource
    private OrderService orderService;
    @Resource
    private SystemParameterService systemParameterService;
    @Autowired
    private MqProducer mqProducer;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Lazy
    @Resource
    private PayStrategyFactory payStrategyFactory;
    @Autowired
    private OfcServiceFacade ofcServiceFacade;
    @Autowired
    private OrderSelfLiftingMapper orderSelfLiftingMapper;
    @Autowired
    private WncServiceFacade wncServiceFacade;
    @Autowired
    private CombineMarketFacade combineMarketFacade;
    @Autowired
    private MarketItemPriceService marketItemPriceService;
    @Lazy
    @Resource
    private OrderItemFeeCalculateService orderItemFeeCalculateService;
    @Resource
    private OfcDeliveryInfoFacade ofcDeliveryInfoFacade;
    @Resource
    private ProductQueryFacade productQueryFacade;
    @Resource
    private ItemSaleLimitConfigService itemSaleLimitConfigService;


    @DubboReference
    private MerchantDeliveryProvider merchantDeliveryProvider;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;
    @Resource
    private OrderNotifyBizService orderNotifyBizService;
    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private UserCenterMerchantStoreGroupFacade userCenterMerchantStoreGroupFacade;
    @Resource
    private OrderInterceptStoreGroupConfig orderInterceptStoreGroupConfig;
    @Resource
    private BizLogFacade bizLogFacade;

    @Resource
    private PlanOrderService planOrderService;
    @DubboReference
    private PlanOrderCommandProvider planOrderCommandProvider;
    @DubboReference
    private PlanOrderQueryProvider planOrderQueryProvider;

    @DubboReference
    private OrderCommandProvider orderCommandProvider;
    @DubboReference
    private CombineOrderCommandProvider combineOrderCommandProvider;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;
    @DubboReference
    private CombineOrderQueryProvider combineOrderQueryProvider;
    @DubboReference
    private OrderAddressQueryProvider orderAddressQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderStatisticsQueryProvider orderStatisticsQueryProvider;
    @DubboReference
    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;

    @DubboReference
    private TenantProvider tenantProvider;


    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    /**
     * 支付单商品名字描述的长度限制：127；huifu & weixin
     */
    private static final int DESC_SIZE_LIMIT = 127;

    /**
     * 查询相同配送日的订单状态
     */
    private static final List<Integer> SAME_DELIVERY_ORDER_STATUS = Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.FINISHED.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode());
    @Resource
    private BusinessTimeConfig businessTimeConfig;
    @Resource
    private PaymentService paymentService;
    @Resource
    private BillProfitSharingSnapshotService billProfitSharingSnapshotService;
    @Resource
    private TenantNonCashCalculationService tenantNonCashCalculationService;
    @Resource
    private TenantFundAccountConfigService tenantFundAccountConfigService;


    /**
     * 预下单
     *
     * @param preOrderDTO
     * @return
     */
    @Override
    public ResultDTO<List<OrderVO>> preOrder(PlaceOrderDTO preOrderDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (CollectionUtils.isEmpty(preOrderDTO.getOrderItemDTOS()) || preOrderDTO.getOrderItemDTOS().stream().anyMatch(orderItemDTO -> Objects.isNull(orderItemDTO.getItemId()))) {
            throw new ProviderException("下单商品不能为空");
        }
        checkPlaceOrder(loginContextInfoDTO.getStoreId ());
        // 定制下单拦截
        interceptCreateOrderByCustom(loginContextInfoDTO);
        // 查询账户绑定地址 判断库存信息是否足够
        MerchantAddress merchantAddress = merchantAddressService.queryDefaultAddress(loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getTenantId());
        AssertCheckParams.notNull(merchantAddress, ResultDTOEnum.DEFAULT_ADDRESS_MISSING.getCode(),
                ResultDTOEnum.DEFAULT_ADDRESS_MISSING.getMessage());

        List<OrderItemDTO> orderItemDTOS = preOrderDTO.getOrderItemDTOS();
        // 组合包商品
        // 如果是组合包下单，订单项转换成组合包子商品
        List<OrderItemDTO> combineOrderItem = orderItemDTOS.stream()
                .filter(e -> ItemTypeEnum.COMBINE_ITEM.getCode().equals(e.getItemType())).collect(Collectors.toList());
        Integer orderType = OrderEnums.OrderType.COMMON.getCode();
        if (!CollectionUtils.isEmpty(combineOrderItem)) {
            orderType = OrderEnums.OrderType.COMBINE.getCode();
            orderItemDTOS = convertCombineOrderItem(combineOrderItem, loginContextInfoDTO.getTenantId());
        }

        List<Long> itemIds = orderItemDTOS.stream().map(OrderItemDTO::getItemId).collect(Collectors.toList());
        // 查询商品信息
        Map<Long, OrderItemDTO> orderItemDTOMap = orderItemDTOS.stream().collect(Collectors.toMap(OrderItem::getItemId, Function.identity(), (k1, k2) -> k1));
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());

        String titles = marketItemVOList.stream()
                .filter(el -> {
                    boolean deleteFlag = Objects.equals(el.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag());
                    if (deleteFlag) {
                        return Boolean.TRUE;
                    }
                    // 获取此次订单中，当前商品是作为组合包子商品还是独立商品售卖
                    OrderItemDTO obj = orderItemDTOMap.get(el.getItemId());
                    if (Objects.isNull(obj)) {
                        return Boolean.TRUE;
                    }
                    // 组合包商品的子商品，下架不影响组合包售卖
                    if (ItemTypeEnum.COMBINE_ITEM.getCode().equals(obj.getItemType())) {
                        return Boolean.FALSE;
                    }
                    if (!OnSaleTypeEnum.ON_SALE.getCode().equals(el.getOnSale())) {
                        return Boolean.TRUE;
                    }
                    return Boolean.FALSE;
                })
                .map(MarketItemVO::getTitle)
                .collect(Collectors.joining(","));
        if (!StringUtils.isBlank(titles)) {
            throw new I18nBizException("{0}商品已失效，请重新选择商品", titles);
        }

        // 校验限购 组合包不需要校验
        if(CollectionUtils.isEmpty(combineOrderItem)) {
            merchantStoreItemSaleLimitCheck(marketItemVOList, orderItemDTOMap, loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId());
            merchantStoreItemBuyMultipleMuCheck(marketItemVOList, orderItemDTOMap);
        }

        Map<Long, MarketItemVO> marketItemVOMap = marketItemVOList.stream()
                .collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));
        // 配送时间 从库存服务获取
        MerchantAddressDTO merchantAddressDTO = MerchantAddressMapperConvert.INSTANCE.address2Dto(merchantAddress);
        CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName(merchantAddress.getCity());
        merchantAddressDTO.setCityId(commonLocationCityDTO.getId());

        // 报价货品ID
        List<Long> quotationSkuIds = marketItemVOList.stream()
                .filter(marketItemVO -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType()))
                .map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> quotationProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(quotationSkuIds)) {
            List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(quotationSkuIds, XianmuSupplyTenant.TENANT_ID);
            quotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 自营货品ID
        List<Long> selfSupportSkuIds = marketItemVOList.stream().filter(marketItemVO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType()))
                .map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> selfSupportProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(selfSupportSkuIds)) {
            List<ProductAgentSkuDTO> selfSupportProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(selfSupportSkuIds, loginContextInfoDTO.getTenantId());
            selfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOS.stream()
                    .collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 获取库存信息
        Map<Long, StockDTO> stockDTOMap = new HashMap<>();
        StockQueryDTO stockQueryDTO = null;
        try {
            // 组装库存查询条件
            stockQueryDTO = assembleStockQueryCondition(orderItemDTOS, marketItemVOMap, loginContextInfoDTO.getTenantId(), merchantAddressDTO,
                    quotationProductAgentSkuDTOMap, selfSupportProductAgentSkuDTOMap);
            stockDTOMap = stockService.preDistributionOrderOccupy(stockQueryDTO);
        } catch (Exception e) {
            log.error("{}获取库存数量失败", stockQueryDTO, e);
        }

        ResultDTO<TenantDTO> resultDTO = tenantService.selectTenantInfo(loginContextInfoDTO.getTenantId());
        TenantDTO tenantDTO = resultDTO.getData();
        // 下单数量
        Map<Long, Integer> itemBuyAmount = orderItemDTOS.stream()
                .collect(Collectors.toMap(OrderItemDTO::getItemId, OrderItemDTO::getAmount));
        // 获取sku价格信息
        Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap;
        // 组合品下单，获取组合品子项金额
        if (!CollectionUtils.isEmpty(combineOrderItem)) {
            OrderItemDTO orderItemDTO = combineOrderItem.get(NumberConstant.ZERO);
            skuMallPriceDTOMap = marketItemPriceService.queryCombineItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId(),
                    orderItemDTO.getAmount (),orderItemDTO.getCombineItemId(),true);
            // 普通商品下单
        } else {
            skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOList, itemBuyAmount, tenantDTO,
                    merchantAddressDTO,true);
        }

        // 自营、报价货品skuId
        List<Long> allSkuIds = marketItemVOList.stream().filter(marketItemVO -> Objects.nonNull(marketItemVO.getSkuId())).map(MarketItemVO::getSkuId).distinct().collect(Collectors.toList());
        List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.querySkuInfo(allSkuIds);
        Map<Long, ProductSkuDetailResp> productSkuDetailRespMap = productSkuDetailResps.stream().collect(Collectors.toMap(ProductSkuDetailResp::getSkuId, Function.identity(), (v1, v2) -> v1));

        // 拆单
        List<OrderVO> orderVOS = splitOrderByDeliveryTenant(marketItemVOMap, orderItemDTOS, stockDTOMap,
                skuMallPriceDTOMap, loginContextInfoDTO, merchantAddressDTO, orderType, productSkuDetailRespMap);

        // 搭售校验
        checkOrderItemSaleMode(orderVOS);

        // 设置多个订单的总金额
        buildMulOrderTotalPrice(orderVOS);

        // 税率，百分位
        BigDecimal taxRate = RpcResultUtil.handle(tenantProvider.getOrderTaxRate(loginContextInfoDTO.getTenantId()));

        // 返回结果
        orderVOS.forEach(orderVO -> {
            BigDecimal payablePrice = orderVO.getPayablePrice();
            // 查询运费
            MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotDTO = queryDeliveryFee(orderVO,
                    loginContextInfoDTO,
                    merchantAddressDTO,
                    TimeUtils.convertTOLocalDate(orderVO.getDeliveryTime()));

            payablePrice = NumberUtil.add(payablePrice, deliveryFeeSnapshotDTO.getDeliveryFee());
            orderVO.setDeliveryFee(deliveryFeeSnapshotDTO.getDeliveryFee());
            orderVO.setPayablePrice(payablePrice);
            orderVO.setPayType(preOrderDTO.getPayType());
            calOrderTaxAmount(orderVO, taxRate);
        });

        // 计算非现金支付可用以及不可用金额
        calculateUsableNonCashAmount(orderVOS);

        return ResultDTO.success(orderVOS);
    }

    private void calculateUsableNonCashAmount(List<OrderVO> orderVOS) {
        if (CollectionUtils.isEmpty(orderVOS)) {
            return;
        }

        LoginContextInfoDTO loginContextInfoDTO = ThreadTokenHolder.getLoginContextInfoDTO();
        Long tenantId = loginContextInfoDTO.getTenantId();
        Long storeId = loginContextInfoDTO.getStoreId();

        // 收集所有订单项的商品ID
        List<Long> itemIds = orderVOS.stream()
                .flatMap(order -> order.getOrderItemVOS().stream())
                .map(OrderItemVO::getItemId)
                .collect(Collectors.toList());

        // 查询商品分类信息
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, tenantId);

        // 商品ID到分类ID的映射
        Map<Long, Long> itemIdToClassificationMap = marketItemVOList.stream()
                .filter(item -> item.getClassificationId() != null)
                .collect(Collectors.toMap(MarketItemVO::getItemId, MarketItemVO::getClassificationId, (v1, v2) -> v1));

        // 构建运费信息
        Map<Long, BigDecimal> deliveryFeeMap = new HashMap<>(orderVOS.size());

        for (OrderVO orderVO : orderVOS) {
            if (orderVO.getOrderId() == null) {
                Long orderId = 1L;
                orderVO.setOrderId(orderId++);
            }
            // 构建商品明细信息
            List<OrderItemNonCashCalculationParamBO> orderItemDetails = new ArrayList<>();
            for (OrderItemVO itemVO : orderVO.getOrderItemVOS()) {
                orderItemDetails.add(OrderItemNonCashCalculationParamBO.builder()
                        .orderId(orderVO.getOrderId())
                        .itemId(itemVO.getItemId())
                        .itemAmount(itemVO.getTotalPrice())
                        .classificationId(itemIdToClassificationMap.get(itemVO.getItemId()))
                        .build());
            }
            deliveryFeeMap.put(orderVO.getOrderId(), orderVO.getDeliveryFee());
            // 构建参数BO
            OrderNonCashCalculationParamBO paramBO = OrderNonCashCalculationParamBO.builder()
                    .tenantId(tenantId)
                    .storeId(storeId)
                    .deliveryFeeMap(deliveryFeeMap)
                    .orderItemDetails(orderItemDetails)
                    .totalOrderAmount(orderVO.getPayablePrice())
                    .build();

            // 执行计算
            OrderNonCashCalculationResultBO resultBO = tenantNonCashCalculationService.calculateNonCashAmount(paramBO);
            orderVO.setUsableNonCashBalance(resultBO.getUsableNonCashBalance());
            orderVO.setUnusableNonCashBalance(resultBO.getUnusableNonCashBalance());
        }
    }

    /**
     * 计算并赋值税费
     * @param orderVO
     * @param taxRate
     */
    private void calOrderTaxAmount(OrderVO orderVO, BigDecimal taxRate){
        if(orderVO == null){
            return;
        }

        orderVO.setTaxRate(taxRate);
        // 税费保留两位小数，四舍五入
        orderVO.setTaxAmount(NumberUtil.div(NumberUtil.mul(orderVO.getPayablePrice(), taxRate), 100).setScale(2, RoundingMode.HALF_UP));
        orderVO.setPayablePrice(NumberUtil.add(orderVO.getPayablePrice(), orderVO.getTaxAmount()));
    }

    /**
     * 定制下单拦截
     *
     * @param loginContextInfoDTO
     */
    private void interceptCreateOrderByCustom(LoginContextInfoDTO loginContextInfoDTO) {
        List<MerchantStoreGroupResultResp> merchantStoreGroupResultResps = userCenterMerchantStoreGroupFacade.getGroupByStoreIds(loginContextInfoDTO.getTenantId(), Collections.singletonList(loginContextInfoDTO.getStoreId()));
        if (CollectionUtil.isEmpty(merchantStoreGroupResultResps)) {
            return;
        }
        MerchantStoreGroupResultResp merchantStoreGroupResultResp = merchantStoreGroupResultResps.get(NumberConstant.ZERO);
        if (Objects.isNull(merchantStoreGroupResultResp)) {
            return;
        }
        boolean intercept = orderInterceptStoreGroupConfig.interceptOrder(loginContextInfoDTO.getTenantId(), merchantStoreGroupResultResp.getMerchantStoreGroupId());
        log.info("定制下单拦截 loginContextInfoDTO:{},merchantStoreGroupResultResp:{},intercept:{}", JSON.toJSONString(loginContextInfoDTO), JSON.toJSONString(merchantStoreGroupResultResp), intercept);
        if (intercept) {
            throw new BizException("抱歉，您已暂时无法通过该方式下单！");
        }
    }

    /**
     * 倍数下单校验
     * @param marketItemVOList
     * @param orderItemDTOMap
     */

    public void merchantStoreItemBuyMultipleMuCheck(List<MarketItemVO> marketItemVOList, Map<Long, OrderItemDTO> orderItemDTOMap) {
        List<String> errorTitle  = new ArrayList<> ();
        for (MarketItemVO marketItemVO : marketItemVOList) {
            OrderItemDTO orderItemDTO = orderItemDTOMap.get(marketItemVO.getItemId());
            if (marketItemVO.getBuyMultipleSwitch ()) {
                if (orderItemDTO.getAmount () % marketItemVO.getBuyMultiple () != 0) {
                    errorTitle.add (marketItemVO.getTitle());
                }
            }
        }
        if(CollectionUtil.isNotEmpty (errorTitle)){
            throw new I18nBizException("[{0}]请按正确的订货倍数下单", errorTitle.stream().collect(Collectors.joining("、")));
        }
    }

    @Override
    public void merchantStoreItemSaleLimitCheck(List<MarketItemVO> marketItemVOList, Map<Long, OrderItemDTO> orderItemDTOMap, Long tenantId, Long storeId) {
        Set<Long> itemIds = marketItemVOList.stream().map(MarketItemVO::getItemId).collect(Collectors.toSet());
        Map<Long, ItemSaleLimitConfigDTO> itemSaleLimitConfigMap = itemSaleLimitConfigService.queryItemSaleLimitConfigMap(tenantId, itemIds);

        for (MarketItemVO marketItemVO : marketItemVOList) {
            ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = itemSaleLimitConfigMap.get(marketItemVO.getItemId());
            // 不存在配置或者无限制
            if (itemSaleLimitConfigDTO == null || ItemSaleLimitRuleEnum.NO_LIMIT.getCode().equals(itemSaleLimitConfigDTO.getSaleLimitRule())) {
                continue;
            }
            ItemSaleLimitRuleEnum itemSaleLimitRuleEnum = ItemSaleLimitRuleEnum.fromCode(itemSaleLimitConfigDTO.getSaleLimitRule());
            int orderQuantitySum = 0;
            OrderItemDTO orderItemDTO = orderItemDTOMap.get(marketItemVO.getItemId());
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime beginDay = null;
            switch (itemSaleLimitRuleEnum) {
                case EVERY_TIME:
                    break;
                case DAILY: beginDay = LocalDateTimeUtil.beginOfDay(now);break;
                case WEEKLY: beginDay = MallDateUtil.startOfWeek(now);break;
                case MONTHLY: beginDay = MallDateUtil.startOfMonth(now);break;
                default:
            }
            orderQuantitySum = orderItemDTO.getAmount() + sumOrderQuantity(tenantId, storeId, beginDay, now, marketItemVO.getItemId());

            if (orderQuantitySum > itemSaleLimitConfigDTO.getSaleLimitQuantity()) {
                throw new I18nBizException("商品[{0}]超过[{1}]最大可购买数量件[{2}]", marketItemVO.getTitle(),itemSaleLimitRuleEnum.getDesc(), itemSaleLimitConfigDTO.getSaleLimitQuantity());
            }

        }
    }

    private Integer sumOrderQuantity(Long tenantId, Long merchantStoreId, LocalDateTime startDay, LocalDateTime endDay, Long itemId) {
        if (startDay == null) {
            return 0;
        }
        Map<Long, Integer> itemQuantity = sumOrderByMerchantStoreId(tenantId, merchantStoreId, startDay, endDay, Lists.newArrayList(itemId));
        return itemQuantity.getOrDefault(itemId, 0);
    }

    private Map<Long, Integer> sumOrderByMerchantStoreId(Long tenantId, Long merchantStoreId, LocalDateTime startDay, LocalDateTime endDay, List<Long> itemIds) {
        // 统计时间范围内未取消的gl
        ItemSaleQuantityReq itemSaleQuantityReq = new ItemSaleQuantityReq();
        itemSaleQuantityReq.setTenantId(tenantId);
        itemSaleQuantityReq.setMerchantStoreId(merchantStoreId);
        itemSaleQuantityReq.setItemIds(itemIds);
        itemSaleQuantityReq.setStartDay(startDay);
        itemSaleQuantityReq.setEndDay(endDay);

        Map<Long, Integer> result = RpcResultUtil.handle(orderStatisticsQueryProvider.countItemSaleQuantity(itemSaleQuantityReq));
        return result;
    }

    /**
     * 组合商品下单转换成子项
     *
     * @param combineOrderItem
     * @return
     */
    private List<OrderItemDTO> convertCombineOrderItem(List<OrderItemDTO> combineOrderItem, Long tenantId) {
        OrderItemDTO orderItemDTO = combineOrderItem.get(NumberConstant.ZERO);
        CombineMarketQueryInputDTO combineMarketQueryInputDTO = new CombineMarketQueryInputDTO();
        combineMarketQueryInputDTO.setItemId(orderItemDTO.getItemId());
        combineMarketQueryInputDTO.setTenantId(tenantId);
        // 查询组合包信息
        CombineMarketDetailDTO combineMarketDetailDTO = combineMarketFacade.combineDetail(combineMarketQueryInputDTO);
        if (Objects.isNull(combineMarketDetailDTO)
                || OnSaleTypeEnum.SOLD_OUT.getCode().equals(combineMarketDetailDTO.getOnSale())) {
            throw new BizException("该组合包商品已下架，请查看别的商品");
        }

        orderItemDTO.setCombineItemId(combineMarketDetailDTO.getCombineItemId());
        orderItemDTO.setCombineMarketId(combineMarketDetailDTO.getCombineMarketId());
        // 组合商品子项
        List<CombineItemDTO> combineItemList = combineMarketDetailDTO.getCombineItemList();
        // 重新组装订单项
        List<OrderItemDTO> orderItemDTOS = combineItemList.stream().map(combineItemDTO -> {
            OrderItemDTO itemDTO = new OrderItemDTO();
            itemDTO.setItemId(combineItemDTO.getMarketItemId());
            itemDTO.setOrderType(OrderEnums.OrderType.COMBINE.getCode());
            itemDTO.setAmount(NumberUtil.mul(combineItemDTO.getQuantity(), orderItemDTO.getAmount()).intValue());
            itemDTO.setItemType(ItemTypeEnum.COMBINE_ITEM.getCode());
            itemDTO.setCombineItemId(combineMarketDetailDTO.getCombineItemId());
            itemDTO.setCombineMarketId(combineMarketDetailDTO.getCombineMarketId());
            return itemDTO;
        }).collect(Collectors.toList());
        return orderItemDTOS;
    }


    /**
     * 构建查询运费的运费的对象
     *
     * @param orderVO
     * @param loginContextInfoDTO
     * @param merchantAddressDTO
     * @param deliveryDate
     * @return
     */
    private MerchantDeliveryFeeSnapshotResp queryDeliveryFee(OrderVO orderVO, LoginContextInfoDTO loginContextInfoDTO,
                                                   MerchantAddressDTO merchantAddressDTO, LocalDate deliveryDate) {
        // 预售订单运费为0
        if(OrderTypeEnum.PRESALE_ORDER.getValue().equals(orderVO.getOrderType())){
            MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotVO = new MerchantDeliveryFeeSnapshotResp();
            deliveryFeeSnapshotVO.setTenantId(loginContextInfoDTO.getTenantId());
            deliveryFeeSnapshotVO.setDeliveryFee(ZERO);
            deliveryFeeSnapshotVO.setRemark("预售订单运费为0");
            return deliveryFeeSnapshotVO;
        }

        DeliveryTotalReq deliveryTotalReq = DeliveryTotalReq.builder()
                .tenantId(loginContextInfoDTO.getTenantId())
                .storeId(loginContextInfoDTO.getStoreId())
                .supplierTenantId(orderVO.getSupplierTenantId())
                .deliveryTime(deliveryDate)
                .orderInfoDTO(buildDeliveryOrderInfo(orderVO, merchantAddressDTO))
                .orderItemInfoDTOList(buildDeliveryOrderItemInfo(orderVO))
                .build();
        deliveryTotalReq.getOrderInfoDTO().setOrderTotalCount(deliveryTotalReq.getOrderItemInfoDTOList().stream()
                .mapToInt(com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO::getItemCount)
                .sum());

        MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotDTO = RpcResultUtil.handle(merchantDeliveryProvider.queryMerchantDeliveryFee(deliveryTotalReq));
        return deliveryFeeSnapshotDTO;
    }

    private com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderInfoDTO buildDeliveryOrderInfo(OrderVO orderVO, MerchantAddressDTO merchantAddressDTO) {
        return com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderInfoDTO.builder()
                .orderTotalPrice(orderVO.getPayablePrice())
                .storeProvince(merchantAddressDTO.getProvince())
                .storeCity(merchantAddressDTO.getCity())
                .storeArea(merchantAddressDTO.getArea())
                .storePoi(merchantAddressDTO.getPoiNote())
                .warehouseNo(orderVO.getWarehouseNo())
                .warehouseType(orderVO.getWarehouseType())
                .fulfillmentType(orderVO.getFulfillmentType())
                .mulOrderTotalPrice(orderVO.getMulOrderTotalPrice())
                .build();
    }

    private List<com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO> buildDeliveryOrderItemInfo(OrderVO orderVO) {
        return orderVO.getOrderItemVOS().stream()
                .map(item -> com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO.builder()
                        .itemId(item.getItemId())
                        .itemCount(item.getAmount())
                        .itemTotalPrice(NumberUtil.mul(item.getPrice(), item.getAmount()))
                        .skuId(item.getSkuId())
                        .goodsType(item.getGoodsType())
                        .weight(item.getWeight())
                        .totalWeight(Optional.ofNullable(item.getWeight()).map(e -> NumberUtil.mul(e, item.getAmount())).orElse(null))
                        .supplierSkuId(item.getSupplierSkuId())
                        .build())
                .collect(Collectors.toList());
    }


    /**
     * 拆分订单
     *
     * @param marketItemVOMap     商品项
     * @param orderItemDTOS       订单项
     * @param stockDTOMap         库存集合
     * @param skuMallPriceDTOMap  sku商城价
     * @param loginContextInfoDTO
     * @return
     */
    private List<OrderVO> splitOrderByDeliveryTenant(Map<Long, MarketItemVO> marketItemVOMap, List<OrderItemDTO> orderItemDTOS, Map<Long, StockDTO> stockDTOMap,
                                                     Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap, LoginContextInfoDTO loginContextInfoDTO, MerchantAddressDTO merchantAddressDTO, Integer orderType, Map<Long, ProductSkuDetailResp> productSkuDetailRespMap) {
        // 查询售后规则
//        List<OrderAfterSaleRuleVO> orderAfterSaleRuleVOS = afterSaleRuleService.queryRule(loginContextInfoDTO.getTenantId());
        List<OrderAfterSaleRuleResp> ruleList = RpcResultUtil.handle(orderAfterSaleRuleQueryProvider.queryByTenantId(loginContextInfoDTO.getTenantId()));

        // 三方仓订单 按照配送时间拆单
        Map<LocalDateTime, OrderVO> thirdOrderVOMap = new HashMap<>();

        // 预售商品拆单，一个预售品一单
        List<OrderVO> presaleItemList = new ArrayList<>();

        // 无仓订单
        OrderVO noWarehouseOrderVO = new OrderVO();
        noWarehouseOrderVO.setOrderType(orderType);
        noWarehouseOrderVO.setSupplierTenantId(loginContextInfoDTO.getTenantId());
        noWarehouseOrderVO.setSupplierName(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getName());
        noWarehouseOrderVO.setWarehouseType(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode());
        noWarehouseOrderVO.setStoreId(loginContextInfoDTO.getStoreId());
        noWarehouseOrderVO.setOrderItemVOS(new ArrayList<>());

        // 自营仓订单
        Map<Long, OrderVO> selfSupplyOrderVOMap = new HashMap<>(16);

        orderItemDTOS.forEach(orderItemDTO -> {
            OrderVO orderVO = null;
            BigDecimal payablePrice = ZERO;
            OrderItemVO orderItemVO = new OrderItemVO();
            MarketItemVO marketItemVO = marketItemVOMap.get(orderItemDTO.getItemId());
            BeanUtils.copyProperties(marketItemVO, orderItemVO);
            orderItemVO.setSpecification(marketItemVO.getSpecification());
            orderItemVO.setSpecificationUnit(marketItemVO.getSpecificationUnit());
            orderItemVO.setItemSaleMode(marketItemVO.getItemSaleMode());
            // 商品的售后信息
            orderItemVO.setAfterSaleUnit(marketItemVO.getAfterSaleUnit());
            orderItemVO.setMaxAfterSaleAmount(marketItemVO.getMaxAfterSaleAmount());
            // 商品数量
            orderItemVO.setAmount(orderItemDTO.getAmount());
            orderItemVO.setItemType(orderItemDTO.getItemType());
            orderItemVO.setOrderType(orderItemDTO.getOrderType());
            orderItemVO.setCombineItemId(orderItemDTO.getCombineItemId());
            // 获取库存信息
            StockDTO stockDTO = stockDTOMap.get(marketItemVO.getItemId());
            if (stockDTO == null || Objects.isNull(stockDTO.getAmount())) {
                throw new I18nBizException("{0}商品库存不足，请稍后下单", marketItemVO.getTitle());
            }

            if (stockDTO.getAmount().compareTo(orderItemVO.getAmount()) < 0 || stockDTO.getOrderNonOccupy()) {
                throw new I18nBizException("{0}商品库存不足，请修改商品数量", marketItemVO.getTitle());
            }

            if (!OrderEnums.OrderType.COMBINE.getCode().equals(orderType)) {
                if (orderItemVO.getAmount().compareTo(marketItemVO.getMiniOrderQuantity()) < 0) {
                    throw new I18nBizException("{0}商品起订量不足，请修改商品数量", marketItemVO.getTitle());
                }
            }

            orderItemVO.setSupplierTenantId(stockDTO.getSupplyTenantId());
            orderItemVO.setSupplierSkuId(stockDTO.getSupplySkuId());
            orderItemVO.setWeight(Optional.ofNullable(productSkuDetailRespMap.get(marketItemVO.getSkuId())).map(ProductSkuDetailResp::getWeight).orElse(null));

            // 三方仓预计配送时间
            LocalDateTime deliveryTime = null;

            // 根据库存进行拆单
            GoodsTypeEnum goodsType = GoodsTypeEnum.getTypeByCode(marketItemVO.getGoodsType());
            switch (goodsType) {
                // 无货商品
                case NO_GOOD_TYPE:
                    orderVO = noWarehouseOrderVO;
                    List<OrderItemVO> orderItemVOS = noWarehouseOrderVO.getOrderItemVOS();
                    orderItemVOS.add(orderItemVO);
                    orderVO.setOrderItemVOS(orderItemVOS);
                    break;
                // 报价货品
                case QUOTATION_TYPE:
                    if(stockDTO.getDeliveryDate() == null){
                        throw new BizException("包含三方优选仓商品不支持配送，请重新选择商品下单");
                    }
                    deliveryTime = stockDTO.getDeliveryDate().atStartOfDay();
                    if(!thirdOrderVOMap.containsKey(deliveryTime)){
                        thirdOrderVOMap.put(deliveryTime, buildThirdOrderVO(orderType, loginContextInfoDTO.getStoreId(), deliveryTime, stockDTO.getFulfillmentType()));
                    }
                    orderVO = thirdOrderVOMap.get(deliveryTime);
                    orderVO.getOrderItemVOS().add(orderItemVO);
                    break;
                // 自营货品
                case SELF_GOOD_TYPE:
                    // 判断是否是自营仓,自营仓根据仓库编号拆单
                    if (marketItemVO.getTenantId().equals(stockDTO.getWarehouseTenantId())) {

                        // 预售品不支持自营仓订单下单
                        if(PresaleSwitchEnum.PRESALE_ITEM.getCode().equals(marketItemVO.getPresaleSwitch())) {
                            throw new BizException("预售品不支持自营仓下单，请重新选择商品下单");
                        }

                        if (selfSupplyOrderVOMap.containsKey(stockDTO.getWarehouseNo())) {
                            orderVO = selfSupplyOrderVOMap.get(stockDTO.getWarehouseNo());
                            orderVO.getOrderItemVOS().add(orderItemVO);
                        } else {
                            orderVO = new OrderVO();
                            orderVO.setOrderType(orderType);
                            orderVO.setSupplierTenantId(loginContextInfoDTO.getTenantId());
                            orderVO.setSupplierName(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getName());
                            orderVO.setWarehouseType(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode());
                            orderVO.setWarehouseNo(stockDTO.getWarehouseNo());
                            orderVO.setStoreId(loginContextInfoDTO.getStoreId());
                            orderVO.setOrderItemVOS(new ArrayList<>(Arrays.asList(orderItemVO)));
                            selfSupplyOrderVOMap.put(stockDTO.getWarehouseNo(), orderVO);
                        }
                        // 如果是代仓货品，订单为鲜沐三方仓
                    } else {
                        // 代仓获取取代仓商城价
                        if(stockDTO.getDeliveryDate() == null){
                            throw new BizException("包含三方优选仓商品不支持配送，请重新选择商品下单");
                        }

                        // 预售品三方仓订单 按单品拆单
                        if(PresaleSwitchEnum.PRESALE_ITEM.getCode().equals(marketItemVO.getPresaleSwitch())){
                            orderVO = buildThirdOrderVO(OrderEnums.OrderType.PRESALE.getCode(), loginContextInfoDTO.getStoreId(), null, stockDTO.getFulfillmentType());
                            orderVO.getOrderItemVOS().add(orderItemVO);
                            presaleItemList.add(orderVO);
                        } else {
                            deliveryTime = stockDTO.getDeliveryDate().atStartOfDay();
                            if (!thirdOrderVOMap.containsKey(deliveryTime)) {
                                thirdOrderVOMap.put(deliveryTime, buildThirdOrderVO(orderType, loginContextInfoDTO.getStoreId(), deliveryTime, stockDTO.getFulfillmentType()));
                            }
                            orderVO = thirdOrderVOMap.get(deliveryTime);
                            orderVO.getOrderItemVOS().add(orderItemVO);
                        }
                    }

                    break;
            }

            orderVO.setCombineItemId(orderItemDTO.getCombineItemId());
            orderVO.setCombineMarketId(orderItemDTO.getCombineMarketId());
            if (Objects.nonNull(orderVO.getPayablePrice())) {
                payablePrice = orderVO.getPayablePrice();
            }

            // 计算商城价格
            SkuMallPriceDTO skuMallPriceDTO = skuMallPriceDTOMap.get(marketItemVO.getItemId());
            if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())
                    || Objects.isNull(skuMallPriceDTO.getSupplyPrice())) {
                BizException exception = new I18nBizException("{0}商品库存不足，请稍后下单", marketItemVO.getTitle());
                log.error("itemId:{}获取商城价失败", marketItemVO.getItemId(), exception);
                throw exception;
            } else {
                // 供应价
                orderItemVO.setSupplyPrice(skuMallPriceDTO.getSupplyPrice());
                // 如果货品为自营货品，订单类型为三方仓，商品价格取代仓商城价
                orderItemVO.setPrice(skuMallPriceDTO.getPrice());
                orderItemVO.setPricingType(skuMallPriceDTO.getPricingType());
                orderItemVO.setPricingNumber(skuMallPriceDTO.getPricingNumber());
            }

            // 售后规则计算
            OrderItemAfterSaleRuleDTO orderAfterSaleRule = getOrderAfterSaleRule(ruleList, marketItemVO.getClassificationId(), orderVO.getWarehouseType());
            // 计算最大可申请售后时间
            Integer applyEndTime = orderAfterSaleRule.getApplyEndTime();
            applyEndTime = Objects.nonNull(orderVO.getApplyEndTime())
                    ? orderVO.getApplyEndTime() < applyEndTime ? applyEndTime : orderVO.getApplyEndTime()
                    : applyEndTime;
            orderVO.setApplyEndTime(applyEndTime);
            orderVO.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
            orderItemVO.setOrderAfterSaleRule(orderAfterSaleRule);

            // 价格计算
            BigDecimal totalPrice = NumberUtil.mul(orderItemVO.getPrice(), orderItemVO.getAmount());
            payablePrice = payablePrice.add(totalPrice);
            orderItemVO.setTotalPrice(totalPrice);
            orderVO.setPayablePrice(payablePrice);
        });

        List<OrderVO> orderVOList = new ArrayList<>();

        // 预售订单
        if (!CollectionUtils.isEmpty(presaleItemList)) {
            presaleItemList.forEach(thirdOrderVO -> {
                recalculateAgentGoodMallPrice(thirdOrderVO, loginContextInfoDTO.getTenantId());
            });

            orderVOList.addAll(presaleItemList);
        }

        // 三方仓订单
        if (!CollectionUtils.isEmpty(thirdOrderVOMap)) {
            thirdOrderVOMap.values().forEach(thirdOrderVO -> {
                recalculateAgentGoodMallPrice(thirdOrderVO, loginContextInfoDTO.getTenantId());
            });

            orderVOList.addAll(thirdOrderVOMap.values());
        }

        // 无仓订单
        if (!CollectionUtils.isEmpty(noWarehouseOrderVO.getOrderItemVOS())) {
            orderVOList.add(noWarehouseOrderVO);
        }

        // 自营仓订单
        List<OrderVO> orderVOS = selfSupplyOrderVOMap.values().stream().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderVOS)) {
            orderVOList.addAll(orderVOS);
        }

        return orderVOList;
    }

    private OrderVO buildThirdOrderVO(Integer orderType, Long storeId, LocalDateTime deliveryTime, Integer fulfillmentType){
        OrderVO thirdOrderVO = new OrderVO();
        thirdOrderVO.setOrderType(orderType);
        thirdOrderVO.setSupplierTenantId(XianmuSupplyTenant.TENANT_ID);
        thirdOrderVO.setSupplierName(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getName());
        thirdOrderVO.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
        thirdOrderVO.setStoreId(storeId);
        thirdOrderVO.setOrderItemVOS(new ArrayList<>());
        thirdOrderVO.setDeliveryTime(deliveryTime);
        thirdOrderVO.setFulfillmentType(fulfillmentType);
        return thirdOrderVO;
    }

    /**
     * 重新计算三方仓订单代仓商品商城价(代仓费用加价之后价格)
     *
     * @param thirdOrderVO
     * @param tenantId
     */
    private void recalculateAgentGoodMallPrice(OrderVO thirdOrderVO, Long tenantId) {
        if(!OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(thirdOrderVO.getWarehouseType())){
            return;
        }
        // 三方仓订单重新计算代仓品价格
        List<OrderItemVO> orderItemVOS = thirdOrderVO.getOrderItemVOS();
        // 代仓品数量累加
        int agentGoodsTotalQuantity = orderItemVOS.stream()
                .filter(e -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(e.getGoodsType()))
                .mapToInt(e -> e.getAmount()).sum();
        if (agentGoodsTotalQuantity > NumberConstant.ZERO) {
            BigDecimal payablePrice = thirdOrderVO.getPayablePrice();
            for (OrderItemVO orderItemVO : orderItemVOS) {
                if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItemVO.getGoodsType())) {
                    // 代仓费用加价之后价格
                    BigDecimal afterPrice = marketItemPriceService.calculateProductAgentSkuFee(tenantId,
                            orderItemVO.getPrice(), agentGoodsTotalQuantity);
                    // 加价前价格
                    orderItemVO.setPrice(afterPrice);
                    BigDecimal beforeTotalPrice = orderItemVO.getTotalPrice();
                    BigDecimal orderItemAfterTotalPrice = NumberUtil.mul(afterPrice, orderItemVO.getAmount());
                    orderItemVO.setTotalPrice(orderItemAfterTotalPrice);
                    payablePrice = NumberUtil.sub(payablePrice, beforeTotalPrice);
                    payablePrice = NumberUtil.add(payablePrice, orderItemAfterTotalPrice);
                }
            }

            thirdOrderVO.setPayablePrice(payablePrice);
        }
    }


    private void checkUnfinishedForcePlanOrder(LoginContextInfoDTO loginContextInfoDTO, String planOrderNo){
        BizException bizException = new BizException("提交订单失败，有需要优先支付的计划单存在，是否去查看和支付？", new BizErrorCode("EXIST_UNFINISHED_FORCE_PLANORDER"));
        UnfinishedPlanOrderVO unfinishedPlanOrderVO = planOrderService.unfinishedForcePlanOrder(loginContextInfoDTO);
        boolean existUnfinishedPlanOrder = unfinishedPlanOrderVO != null && unfinishedPlanOrderVO.isExistUnfinishedPlanOrder();
        // 计划单号为空, 普通下单, 校验是否有未完成铺货单
        if(org.apache.commons.lang3.StringUtils.isBlank(planOrderNo)){
            if(existUnfinishedPlanOrder){
                throw bizException;
            }
        }else{
            // 计划单号有值，判断是否是铺货单类型（铺货单直接下单），非铺货单 校验是否有未完成铺货单，异常提示
            PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(planOrderNo));
            boolean isForcePlanOrder = planOrderDetailResp != null && AgentOrderEnum.PlanTypeEnum.CREATE_FORCE_PLAN_ORDER.name().equals(planOrderDetailResp.getPlanType());
            if(!isForcePlanOrder && existUnfinishedPlanOrder){
                throw bizException;
            }
        }
    }


    /**
     * 下单
     *
     * @param preOrderDTOList
     * @param loginContextInfoDTO
     * @return
     */
    @Override
    public ResultDTO<List<String>> newPlaceOrder(List<PlaceOrderDTO> preOrderDTOList, LoginContextInfoDTO loginContextInfoDTO) {
        SystemParameters systemParameters = systemParameterService.selectByKey(SysParamKeyEnum.PRODUCT_TEST_PHONE.getKey());
        if (Objects.nonNull(systemParameters)
                && Objects.equals(systemParameters.getParamValue(), loginContextInfoDTO.getPhone())) {
            throw new BizException("体验账号不能进行下单");
        }
        checkPlaceOrder(loginContextInfoDTO.getStoreId ());

        // 校验是否有未完成的铺货单
        checkUnfinishedForcePlanOrder(loginContextInfoDTO, preOrderDTOList.get(NumberConstant.ZERO).getPlanOrderNo());


        // 校验订单，获取价格和库存 生成订单
        List<OrderVO> orderVOS = checkOrder(preOrderDTOList, loginContextInfoDTO);

        Long merchantAddressId = preOrderDTOList.get(NumberConstant.ZERO).getMerchantAddressId();
        Long merchantContactId = preOrderDTOList.get(NumberConstant.ZERO).getMerchantContactId();
        // 查询联系人地址
        MerchantAddressVO merchantAddressVO = merchantAddressService.queryMerchantAddress(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId(), merchantAddressId,
                merchantContactId);
        // 处理订单
        dealOrderNew(orderVOS, merchantAddressVO, loginContextInfoDTO);
        // 三方仓和自营仓发送扣减库存
        orderVOS.stream().filter(
                        orderVO -> !OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderVO.getWarehouseType()))
                .forEach(orderVO -> {
                    // 扣减库存
                    localStock(orderVO, merchantAddressVO, loginContextInfoDTO);
                });

        List<String> orderNos = orderVOS.stream().map(OrderVO::getOrderNo).collect(Collectors.toList());

        // 计划单下单更新成功，小程序发起的下单
        if(!preOrderDTOList.get(NumberConstant.ZERO).isAutoPlanOrderCreateOrder()) {
            createOrderSuccess(preOrderDTOList.get(NumberConstant.ZERO).getPlanOrderNo(), orderNos);
        }

        return ResultDTO.success(orderNos);
    }

    private void checkPlaceOrder(Long storeId) {
        String expiryTime = merchantStoreService.getPlaceOrderPermissionExpiryTime (storeId);
        if(org.apache.commons.lang3.StringUtils.isNotBlank (expiryTime) && LocalDateTime.now().isAfter(LocalDateTimeUtil.parse (expiryTime,"yyyy-MM-dd HH:mm:ss"))){
            throw new BizException ("门店下单有效期已过，请联系总部");
        }
    }


    private void createOrderSuccess(String planOrderNo, List<String> orderNoList){
        if(StringUtils.isEmpty(planOrderNo)){
            return;
        }
        try {
            CreateOrderSuccessReq createOrderSuccessReq = new CreateOrderSuccessReq();
            createOrderSuccessReq.setPlanOrderNo(planOrderNo);
            createOrderSuccessReq.setOrderNoList(orderNoList);

            RpcResponseUtil.handler(planOrderCommandProvider.updateCreateOrderSuccess(createOrderSuccessReq));
        } catch (Exception e) {
            log.error("更新计划单下单成功异常，planOrderNo={}", planOrderNo, e);
        }
    }

    /**
     * 提交订单检查和校验
     *
     * @param preOrderDTOList
     * @param loginContextInfoDTO
     */
    private List<OrderVO> checkOrder(List<PlaceOrderDTO> preOrderDTOList, LoginContextInfoDTO loginContextInfoDTO) {
        List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        preOrderDTOList.forEach(preOrderDTO -> {
            orderItemDTOS.addAll(preOrderDTO.getOrderItemDTOS());
        });

        // 判断是否是组合订单,是组合订单进行校验
        PlaceOrderDTO placeOrderDTO = preOrderDTOList.get(NumberConstant.ZERO);
        if (OrderEnums.OrderType.COMBINE.getCode().equals(placeOrderDTO.getOrderType())) {
            checkCombineItem(placeOrderDTO.getCombineItemId(), loginContextInfoDTO.getTenantId(), orderItemDTOS);
        }

        List<Long> itemIds = orderItemDTOS.stream().map(OrderItemDTO::getItemId).collect(Collectors.toList());
        // 查询商品信息
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());
        if (!OrderEnums.OrderType.COMBINE.getCode().equals(placeOrderDTO.getOrderType())) {
            String titles = marketItemVOList.stream()
                    .filter(el -> Objects.equals(el.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag())
                            || !Objects.equals(OnSaleTypeEnum.ON_SALE.getCode(), el.getOnSale()))
                    .map(MarketItemVO::getTitle)
                    .collect(Collectors.joining(","));
            if (!StringUtils.isBlank(titles)) {
                throw new I18nBizException("{0}商品已失效，请重新选择商品", titles);
            }
        }

        Map<Long, MarketItemVO> marketItemVOMap = marketItemVOList.stream().collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));

        // 查询账户绑定地址 判断库存信息是否足够
        MerchantAddress merchantAddress = merchantAddressService.queryDefaultAddress(loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getTenantId());
        // 配送时间 从库存服务获取
        MerchantAddressDTO merchantAddressDTO = MerchantAddressMapperConvert.INSTANCE.address2Dto(merchantAddress);
        CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName(merchantAddress.getCity());
        merchantAddressDTO.setCityId(commonLocationCityDTO.getId());

        // 报价货品skuId
        List<Long> quotationSkuIds = marketItemVOList.stream().filter(marketItemVO -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType()))
                .map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> quotationProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(quotationSkuIds)) {
            List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(quotationSkuIds, XianmuSupplyTenant.TENANT_ID);
            quotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 自营货品ID
        List<Long> selfSupportSkuIds = marketItemVOList.stream().filter(marketItemVO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType()))
                .map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> selfSupportProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(selfSupportSkuIds)) {
            List<ProductAgentSkuDTO> selfSupportProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(selfSupportSkuIds, loginContextInfoDTO.getTenantId());
            selfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOS.stream()
                    .collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 自营、报价货品skuId
        List<Long> allSkuIds = marketItemVOList.stream().filter(marketItemVO -> Objects.nonNull(marketItemVO.getSkuId())).map(MarketItemVO::getSkuId).distinct().collect(Collectors.toList());
        List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.querySkuInfo(allSkuIds);
        Map<Long, ProductSkuDetailResp> productSkuDetailRespMap = productSkuDetailResps.stream().collect(Collectors.toMap(ProductSkuDetailResp::getSkuId, Function.identity(), (v1, v2) -> v1));

        // 获取库存信息
        Map<Long, StockDTO> stockDTOMap = new HashMap<>();
        StockQueryDTO stockQueryDTO = null;
        try {
            // 组装库存查询条件
            stockQueryDTO = assembleStockQueryCondition(orderItemDTOS, marketItemVOMap, loginContextInfoDTO.getTenantId(), merchantAddressDTO,
                    quotationProductAgentSkuDTOMap, selfSupportProductAgentSkuDTOMap);
            stockDTOMap = stockService.preDistributionOrderOccupy(stockQueryDTO);
        } catch (Exception e) {
            BizException bizException = new BizException("商品库存不足，请重新下单");
            log.error("{}获取库存数量失败", stockQueryDTO, e);
            throw bizException;
        }

        // 下单数量
        Map<Long, Integer> itemBuyAmount = orderItemDTOS.stream()
                .collect(Collectors.toMap(OrderItemDTO::getItemId, OrderItemDTO::getAmount));
        ResultDTO resultDTO = tenantService.selectTenantInfo(loginContextInfoDTO.getTenantId());
        TenantDTO tenantDTO = (TenantDTO) resultDTO.getData();

        // 获取sku价格信息
        Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap;
        // 组合品下单，获取组合品子项金额
        if (OrderEnums.OrderType.COMBINE.getCode().equals(placeOrderDTO.getOrderType())) {
            CombineMarketQueryInputDTO combineMarketQueryInputDTO = new CombineMarketQueryInputDTO();
            combineMarketQueryInputDTO.setItemId(placeOrderDTO.getCombineItemId());
            combineMarketQueryInputDTO.setTenantId(loginContextInfoDTO.getTenantId());
            CombineMarketDetailDTO combineMarketDetailDTO = combineMarketFacade.combineDetail (combineMarketQueryInputDTO);
            if(combineMarketDetailDTO == null){
                throw new BizException ("组合包商品不存在，请重新下单");
            }
            Long itemId = orderItemDTOS.get (0).getItemId ();
            Optional<CombineItemDTO> first = combineMarketDetailDTO.getCombineItemList ().stream ().filter (combineItemDTO -> Objects.equals (combineItemDTO.getMarketItemId (), itemId)).findFirst ();
            if(!first.isPresent ()){
                throw new BizException ("组合包商品不存在，请重新下单");
            }
            Integer quantity = first.get ().getQuantity ();

            skuMallPriceDTOMap = marketItemPriceService.queryCombineItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId(),itemBuyAmount.get (itemId)/quantity,placeOrderDTO.getCombineItemId(),true);
            // 普通商品下单
        } else {
            skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOList, itemBuyAmount, tenantDTO,
                    merchantAddressDTO,true);
        }
        // 组装订单
        List<OrderVO> orderVOS = assembleOrder(preOrderDTOList, marketItemVOMap, skuMallPriceDTOMap, stockDTOMap,
                loginContextInfoDTO, merchantAddressDTO, productSkuDetailRespMap);
        // 搭售校验
        checkOrderItemSaleMode(orderVOS);
        return orderVOS;
    }

    private void checkOrderItemSaleMode(List<OrderVO> orderVOS) {
        if (CollectionUtil.isEmpty(orderVOS)) {
            return;
        }
        List<OrderItemVO> orderItemVOList = orderVOS.stream().flatMap(orderVO -> orderVO.getOrderItemVOS().stream()).collect(Collectors.toList());
        Optional<OrderItemVO> first = orderItemVOList.stream().filter(orderItemVO -> {
            // 订单商品项列表是否包含可独售商品(组合包是可独售商品)
            boolean containsNormalSale = ItemTypeEnum.COMBINE_ITEM.getCode().equals(orderItemVO.getItemType()) || ItemSaleModeEnum.NORMAL_SALE.getMode().equals(orderItemVO.getItemSaleMode());
            return containsNormalSale;
        }).findFirst();
        if (!first.isPresent()) {
            log.warn("搭售校验拦截 orderItemVOList:{}", JSON.toJSONString(orderItemVOList));
            throw new BizException("下单规则发生了变化，请返回上一页重新调整后再试喔");
        }
    }

    /**
     * 下单校验组合商品
     *
     * @param combineItemId
     * @param tenantId
     * @param orderItemDTOS
     */
    private void checkCombineItem(Long combineItemId, Long tenantId, List<OrderItemDTO> orderItemDTOS) {
        CombineMarketQueryInputDTO combineMarketQueryInputDTO = new CombineMarketQueryInputDTO();
        combineMarketQueryInputDTO.setItemId(combineItemId);
        combineMarketQueryInputDTO.setTenantId(tenantId);
        // 查询组合包信息
        CombineMarketDetailDTO combineMarketDetailDTO = combineMarketFacade.combineDetail(combineMarketQueryInputDTO);
        if (Objects.isNull(combineMarketDetailDTO)
                || OnSaleTypeEnum.SOLD_OUT.getCode().equals(combineMarketDetailDTO.getOnSale())) {
            throw new BizException("该组合包商品已下架，请查看别的商品");
        }

        // 判断组合包商品子项是否已变更
        List<CombineItemDTO> combineItemList = combineMarketDetailDTO.getCombineItemList();
        Map<Long, CombineItemDTO> combineItemDTOMap = combineItemList.stream()
                .collect(Collectors.toMap(CombineItemDTO::getMarketItemId, item -> item));
        List<Long> itemIds = combineItemList.stream().map(CombineItemDTO::getMarketItemId).sorted()
                .collect(Collectors.toList());
        List<Long> itemIdList = orderItemDTOS.stream().map(OrderItemDTO::getItemId).sorted()
                .collect(Collectors.toList());
        if (!itemIds.equals(itemIdList)) {
            throw new BizException("该组合包商品已变更，请返回商品页重新下单");
        }

        // 获取单个组合包子项数量配置
        for (OrderItemDTO orderItemDTO : orderItemDTOS) {
            CombineItemDTO combineItemDTO = combineItemDTOMap.get(orderItemDTO.getItemId());
            // 查看子项数量是否变更
            // TODO 需要组合包的下单数量 orderAmount, (orderItemDTO.getAmount()/orderAmount) == combineItemDTO.getQuantity(), 不等异常
            if (orderItemDTO.getAmount() % combineItemDTO.getQuantity() != NumberConstant.ZERO) {
                throw new BizException("该组合包商品已变更，请返回商品页重新下单");
            }

            orderItemDTO.setCombineItemQuantity(combineItemDTO.getQuantity());
        }
    }

    /**
     * 组装订单
     *
     * @param preOrderDTOList
     * @param marketItemVOMap
     * @param skuMallPriceDTOMap
     * @param stockDTOMap
     */
    private List<OrderVO> assembleOrder(List<PlaceOrderDTO> preOrderDTOList, Map<Long, MarketItemVO> marketItemVOMap, Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap,
                                        Map<Long, StockDTO> stockDTOMap, LoginContextInfoDTO loginContextInfoDTO, MerchantAddressDTO merchantAddressDTO, Map<Long, ProductSkuDetailResp> productSkuDetailRespMap) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 查询售后规则
        List<OrderAfterSaleRuleResp> ruleList = RpcResultUtil.handle(orderAfterSaleRuleQueryProvider.queryByTenantId(tenantId));
        List<OrderVO> orderVOS = preOrderDTOList.stream().map(preOrderDTO -> {
            LocalDate deliveryDate = null;

            OrderVO orderVO = new OrderVO();
            orderVO.setTenantId(tenantId);
            orderVO.setRemark(preOrderDTO.getRemark());
            orderVO.setWarehouseType(preOrderDTO.getWarehouseType());
            orderVO.setWarehouseNo(preOrderDTO.getWarehouseNo());
            orderVO.setPayType(preOrderDTO.getPayType());
            orderVO.setOrderType(preOrderDTO.getOrderType());
            orderVO.setCombineItemId(preOrderDTO.getCombineItemId());
            orderVO.setPlanOrderNo(preOrderDTO.getPlanOrderNo());
            if(!StringUtils.isEmpty(preOrderDTO.getPlanOrderNo())){
                orderVO.setOrderSource(OrderSourceEnum.AGENT_ORDER.getValue());
            }
            Long supplierTenantId = OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode()
                    .equals(preOrderDTO.getWarehouseType()) ? XianmuSupplyTenant.TENANT_ID : tenantId;
            orderVO.setSupplierTenantId(supplierTenantId);

            // 三方仓订单设置配送日期
            if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(preOrderDTO.getWarehouseType())) {
                List<LocalDate> deliveryDateList = preOrderDTO.getOrderItemDTOS().stream()
                        .map(e -> {
                            StockDTO stockDTO = stockDTOMap.get(e.getItemId());
                            if(stockDTO == null){
                                return null;
                            }
                            return stockDTO.getDeliveryDate();
                        }).collect(Collectors.toList());

                // 单个三方仓订单, 存在sku配送时间为空
                boolean hasNullDeliveryDate = deliveryDateList.stream().anyMatch(Objects::isNull);
                if(hasNullDeliveryDate){
                    throw new BizException("包含三方优选仓商品不支持配送，请重新选择商品下单");
                }

                // 单个三方仓订单，如果存在多个配送时间，异常提示，重新下单拆单
                boolean hasManyDeliveryDate = deliveryDateList.stream().filter(Objects::nonNull).distinct().count() > 1;
                if(hasManyDeliveryDate){
                    throw new BizException("三方优选仓商品配送时间变动，请重新选择商品下单");
                }

                deliveryDate = deliveryDateList.get(0);
                if(!OrderTypeEnum.PRESALE_ORDER.getValue().equals(orderVO.getOrderType())) {
                    orderVO.setDeliveryTime(TimeUtils.convertTOLocalDate(deliveryDate));
                }
                orderVO.setFulfillmentType(preOrderDTO.getOrderItemDTOS().stream().findFirst().map(e -> stockDTOMap.get(e.getItemId())).map(StockDTO::getFulfillmentType).orElse(FulfillmentTypeEnum.CITY_DELIVERY.getValue()));
            }


            BigDecimal payablePrice = ZERO;
            List<OrderItemDTO> orderItemDTOS = preOrderDTO.getOrderItemDTOS();
            List<OrderItemVO> orderItemVOS = new ArrayList<>();
            for (OrderItemDTO orderItemDTO : orderItemDTOS) {
                OrderItemVO orderItemVO = new OrderItemVO();
                MarketItemVO marketItemVO = marketItemVOMap.get(orderItemDTO.getItemId());
                BeanUtils.copyProperties(marketItemVO, orderItemVO);
                orderItemVO.setSpecification(marketItemVO.getSpecification());
                orderItemVO.setSpecificationUnit(marketItemVO.getSpecificationUnit());
                orderItemVO.setItemSaleMode(marketItemVO.getItemSaleMode());
                orderItemVO.setItemCode(marketItemVO.getItemCode());

                // 商品的售后信息
                orderItemVO.setAfterSaleUnit(marketItemVO.getAfterSaleUnit());
                orderItemVO.setMaxAfterSaleAmount(marketItemVO.getMaxAfterSaleAmount());
                // 商品数量
                orderItemVO.setAmount(orderItemDTO.getAmount());
                orderItemVO.setItemType(orderItemDTO.getItemType());
                orderItemVO.setOrderType(orderItemDTO.getOrderType());
                // 组合品ItemId
                orderItemVO.setCombineItemId(orderItemDTO.getCombineItemId());
                OrderCombineItemVO orderCombineItemVO = new OrderCombineItemVO();
                orderCombineItemVO.setQuantity(orderItemDTO.getCombineItemQuantity());
                orderItemVO.setOrderCombineItemVO(orderCombineItemVO);
                // 获取库存信息
                StockDTO stockDTO = stockDTOMap.get(marketItemVO.getItemId());
                if (stockDTO == null || Objects.isNull(stockDTO.getAmount())) {
                    throw new I18nBizException("{0}商品库存不足，请稍后下单", marketItemVO.getTitle());
                }

                if (stockDTO.getAmount().compareTo(orderItemVO.getAmount()) < 0 || stockDTO.getOrderNonOccupy()) {
                    throw new I18nBizException("{0}商品库存不足，请修改商品数量", marketItemVO.getTitle());
                }

                if (!OrderEnums.OrderType.COMBINE.getCode().equals(preOrderDTO.getOrderType())) {
                    if (orderItemVO.getAmount().compareTo(marketItemVO.getMiniOrderQuantity()) < 0) {
                        throw new I18nBizException("{0}商品起订量不足，请修改商品数量", marketItemVO.getTitle());
                    }
                }

                // 自营仓判断仓库编号是否一致，不一致不能进行下单
                if (OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(preOrderDTO.getWarehouseType())) {
                    if (!preOrderDTO.getWarehouseNo().equals(stockDTO.getWarehouseNo())) {
                        throw new I18nBizException("{0}商品库存不足，请稍候下单", marketItemVO.getTitle());
                    }
                }

                // 三方仓订单，快递履约，代销不入仓品不能下单
                if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(preOrderDTO.getWarehouseType())) {
                    Integer subAgentType = Optional.ofNullable(productSkuDetailRespMap.get(stockDTO.getSkuId())).map(ProductSkuDetailResp::getSubAgentType).orElse(null);

                    if(FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(stockDTO.getFulfillmentType()) && SubAgentTypeEnum.CONSIGNMENT_NOT_WAREHOUSING.getType().equals(subAgentType)){
                        log.error("itemId={}, title={}为代销不入仓品，storeId={}快递履约不支持下单", marketItemVO.getItemId(), marketItemVO.getTitle(), loginContextInfoDTO.getStoreId());
                        throw new I18nBizException("{0}商品为代销不入仓品，快递履约不支持下单", marketItemVO.getTitle());
                    }
                }


                orderItemVO.setSupplierTenantId(stockDTO.getSupplyTenantId());
                orderItemVO.setSupplierSkuId(stockDTO.getSupplySkuId());
                orderItemVO.setSupplySku(stockDTO.getSupplySku());
                orderItemVO.setSkuCode(stockDTO.getSupplySku());
                if(Objects.nonNull(stockDTO.getSkuId())) {
                    orderItemVO.setCustomSkuCode(Optional.ofNullable(productSkuDetailRespMap.get(stockDTO.getSkuId())).map(ProductSkuDetailResp::getCustomSkuCode).orElse(null));
                }

                if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(preOrderDTO.getWarehouseType())) {
                    orderItemVO.setWeight(marketItemVO.getWeight());
                } else {
                    orderItemVO.setWeight(Optional.ofNullable(productSkuDetailRespMap.get(stockDTO.getSkuId())).map(ProductSkuDetailResp::getWeight).orElse(null));
                }

                // 计算商城价格
                SkuMallPriceDTO skuMallPriceDTO = skuMallPriceDTOMap.get(marketItemVO.getItemId());
                if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())
                        || Objects.isNull(skuMallPriceDTO.getSupplyPrice())) {
                    BizException ex = new I18nBizException("{0}商品库存不足，请稍后下单", marketItemVO.getTitle());
                    log.error("skuId:{}获取商城价失败", marketItemVO.getSkuId(), ex);
                    throw ex;
                } else {
                    // 供应价
                    orderItemVO.setSupplyPrice(skuMallPriceDTO.getSupplyPrice());
                    orderItemVO.setPrice(skuMallPriceDTO.getPrice());
                    orderItemVO.setPricingType(skuMallPriceDTO.getPricingType());
                    orderItemVO.setPricingNumber(skuMallPriceDTO.getPricingNumber());
                    orderCombineItemVO.setOriginalPrice(skuMallPriceDTO.getMarketItemPrice());
                }

                // 售后规则计算
                OrderItemAfterSaleRuleDTO orderAfterSaleRule = getOrderAfterSaleRule(ruleList, marketItemVO.getClassificationId(), preOrderDTO.getWarehouseType());
                // 计算最大可申请售后时间
                Integer applyEndTime = orderAfterSaleRule.getApplyEndTime();
                if(FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(stockDTO.getFulfillmentType())){
                    applyEndTime = orderAfterSaleRule.getExpressOrderApplyEndTime();
                }else {
                    applyEndTime = Objects.nonNull(orderVO.getApplyEndTime())
                            ? orderVO.getApplyEndTime() < applyEndTime ? applyEndTime : orderVO.getApplyEndTime()
                            : applyEndTime;
                }

                orderItemVO.setOrderAfterSaleRule(orderAfterSaleRule);
                orderVO.setApplyEndTime(applyEndTime);
                orderVO.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
                // 价格计算
                BigDecimal totalPrice = NumberUtil.mul(orderItemVO.getPrice(), orderItemVO.getAmount());
                payablePrice = payablePrice.add(totalPrice);
                orderItemVO.setTotalPrice(totalPrice);
                orderItemVOS.add(orderItemVO);
            }

            orderVO.setOrderItemVOS(orderItemVOS);
            orderVO.setPayablePrice(payablePrice);

            return orderVO;
        }).collect(Collectors.toList());

        // 三方仓商品价格重新计算
        orderVOS.forEach(orderVO -> {
            if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderVO.getWarehouseType())) {
                recalculateAgentGoodMallPrice(orderVO, orderVO.getTenantId());
            }
        });

        // 设置多个订单的总金额
        buildMulOrderTotalPrice(orderVOS);

        // 税率，百分位
        BigDecimal taxRate = RpcResultUtil.handle(tenantProvider.getOrderTaxRate(loginContextInfoDTO.getTenantId()));

        // 运费计算
        orderVOS.forEach(orderVO -> {
            BigDecimal payablePrice = orderVO.getPayablePrice();
            // 查询运费
            MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotResp = queryDeliveryFee(orderVO, loginContextInfoDTO, merchantAddressDTO, TimeUtils.convertTOLocalDate(orderVO.getDeliveryTime()));
            payablePrice = NumberUtil.add(payablePrice, deliveryFeeSnapshotResp.getDeliveryFee());
            orderVO.setDeliveryFee(deliveryFeeSnapshotResp.getDeliveryFee());
            orderVO.setPayablePrice(payablePrice);
            orderVO.setDeliveryFeeSnapshotVO(OrderDeliveryConvert.INSTANCE.convert2Dto(deliveryFeeSnapshotResp));
            calOrderTaxAmount(orderVO, taxRate);

        });

        return orderVOS;
    }

    private void buildMulOrderTotalPrice(List<OrderVO> orderVOS){
        if(CollectionUtils.isEmpty(orderVOS)){
            return;
        }

        // 一批下单拆单后多个订单的总金额，不包含运费
        BigDecimal mulOrderTotalPrice = orderVOS.stream()
                .map(order -> Optional.ofNullable(order.getPayablePrice()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        orderVOS.forEach(orderVO -> {
            orderVO.setMulOrderTotalPrice(mulOrderTotalPrice);
        });
    }

    /**
     * 组装库存查询数据
     *
     * @param orderItemDTOS                    下单项
     * @param marketItemVOMap                  商品项
     * @param tenantId                         品牌方Id
     * @param merchantAddressDTO               门店地址
     * @param quotationProductAgentSkuDTOMap   报价货品和鲜沐商品映射关系
     * @param selfSupportProductAgentSkuDTOMap 自营货品和鲜沐商品映射关系
     * @return
     */
    public StockQueryDTO assembleStockQueryCondition(List<OrderItemDTO> orderItemDTOS, Map<Long, MarketItemVO> marketItemVOMap, Long tenantId,
                                                     MerchantAddressDTO merchantAddressDTO, Map<Long, ProductAgentSkuDTO> quotationProductAgentSkuDTOMap, Map<Long, ProductAgentSkuDTO> selfSupportProductAgentSkuDTOMap) {
        StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setTenantId(tenantId);
        stockQueryDTO.setMerchantAddressDTO(merchantAddressDTO);
        Map<Long, ProductAgentSkuDTO> finalQuotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOMap;
        Map<Long, ProductAgentSkuDTO> finalSelfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOMap;
        List<PreDistributionOrderItemDTO> preDistributionOrderItemDTOS = orderItemDTOS.stream().map(trolley -> {
            Long itemId = trolley.getItemId();
            MarketItemVO marketItemVO = marketItemVOMap.get(itemId);
            PreDistributionOrderItemDTO preDistributionOrderItemDTO = new PreDistributionOrderItemDTO();
            preDistributionOrderItemDTO.setItemId(itemId);
            preDistributionOrderItemDTO.setQuantity(trolley.getAmount());
            preDistributionOrderItemDTO.setGoodsType(marketItemVO.getGoodsType());
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType())) {
                ProductAgentSkuDTO productAgentSkuDTO = finalSelfSupportProductAgentSkuDTOMap
                        .get(marketItemVO.getSkuId());
                preDistributionOrderItemDTO.setSkuId(marketItemVO.getSkuId());
                preDistributionOrderItemDTO.setAgentTenantId(productAgentSkuDTO.getAgentTenantId());
                preDistributionOrderItemDTO.setAgentSku(productAgentSkuDTO.getAgentSkuCode());
                preDistributionOrderItemDTO.setAgentSkuId(productAgentSkuDTO.getAgentSkuId());
            } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType())) {
                ProductAgentSkuDTO productAgentSkuDTO = finalQuotationProductAgentSkuDTOMap
                        .get(marketItemVO.getSkuId());
                preDistributionOrderItemDTO.setAgentTenantId(productAgentSkuDTO.getAgentTenantId());
                preDistributionOrderItemDTO.setSkuId(marketItemVO.getSkuId());
                preDistributionOrderItemDTO.setAgentSku(productAgentSkuDTO.getAgentSkuCode());
                preDistributionOrderItemDTO.setAgentSkuId(productAgentSkuDTO.getAgentSkuId());
            }

            return preDistributionOrderItemDTO;
        }).collect(Collectors.toList());
        stockQueryDTO.setPreDistributionOrderItemDTOList(preDistributionOrderItemDTOS);
        return stockQueryDTO;
    }

    public void dealOrderNew(List<OrderVO> orderVOS, MerchantAddressVO merchantAddressVO,
                             LoginContextInfoDTO loginContextInfoDTO) {
        createCombineOrder(orderVOS);

        // 生成订单号
        orderVOS.stream().forEach(
                orderVO -> {
                    // 生成订单号
                    String orderNo = Global.createOrderNo(Global.NORMAL_ORDER_CODE);
                    orderVO.setOrderNo(orderNo);
                    List<OrderItemVO> orderItemVOS = orderVO.getOrderItemVOS();
                    //  如果无仓先扣库存
                    if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderVO.getWarehouseType())) {
                        // 扣减库存
                        localStock(orderVO, merchantAddressVO, loginContextInfoDTO);
                    }

                    // 创建订单
                    createOrder(orderVO, merchantAddressVO, loginContextInfoDTO);
                    // 删除购物车商品
                    List<Long> itemIds = orderItemVOS.stream().map(OrderItemVO::getItemId).collect(Collectors.toList());
                    trolleyMapper.batchDelete(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId(), itemIds);
                    // 发送延时消息
                    sendOrderCancelMessage(orderVO.getOrderId(), OrderCancelTypeEnum.NORMAL_CANCEL.getType(), businessTimeConfig.getOrderCancelTime());
                }
        );
    }

    /**
     * 发送订单取消延时消息
     *
     * @param orderId
     * @param type    0、手动取消 1、延时消息取消 2、超时延时消息取消
     */
    @Override
    public void sendOrderCancelMessage(Long orderId, Integer type, Long delayTime) {
        OrderCancelDTO orderCancelDTO = new OrderCancelDTO();
        orderCancelDTO.setOrderId(orderId);
        orderCancelDTO.setCancelType(type);

        DelayData delayData = new DelayData();
        delayData.setType(MQType.ORDER_TIMEOUT_CLOSE_V2);
        delayData.setData(JSONObject.toJSONString(orderCancelDTO));
        log.info("订单：[{}], 延迟消息时长：[{}] 分钟", orderId, delayTime);
        mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null, JSONObject.toJSONString(delayData), delayTime * 60 * 1000);
    }

    /**
     * 更新订单金额
     *
     * @param orderNoList
     * @return
     */
    @Override
    public OrderAmountRefreshVO refreshOrderAmount(List<String> orderNoList) {
        OrderAmountRefreshVO refreshVO = OrderAmountRefreshVO.builder()
            .refreshFlag(false)
            .build();
        // 1.查询原订单信息
        OrderAmountRefreshTotalDTO orderAmountRefreshTotalDTO = getRefreshOrder(orderNoList);
        if (Objects.isNull(orderAmountRefreshTotalDTO) || Objects.isNull(orderAmountRefreshTotalDTO.getOrderResp())){
            return refreshVO;
        }

        OrderResp orderDTO = orderAmountRefreshTotalDTO.getOrderResp();

        refreshVO.setOriDeliveryFee(orderDTO.getDeliveryFee());

        List<OrderItemAndSnapshotResp> orderItemDTOS = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));
        if (CollectionUtils.isEmpty(orderItemDTOS)) {
            throw new ParamsException("未找到订单项！");
        }
        OrderAddressResp orderAddressDTO = RpcResultUtil.handle(orderAddressQueryProvider.queryByOrderId(orderDTO.getTenantId(), orderDTO.getId()));

        // 2.加锁
        String redisKey = RedisKeyEnum.C00007.join(orderDTO.getOrderNo());
        RLock lock = redissonClient.getLock(redisKey);
        // 未获取到锁，退出
        if (!lock.tryLock()) {
            throw new BizException("该订单正在被其他人刷新支付，请稍后重试");
        }
        try {
            // 3.查询新运费
            DeliveryTotalReq deliveryTotalDTO = merchantDeliveryFeeService.buildDeliveryParam(orderDTO, orderItemDTOS, orderAddressDTO, orderAmountRefreshTotalDTO.getMulOrderTotalPrice());
            MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotResp = RpcResultUtil.handle(merchantDeliveryProvider.queryMerchantDeliveryFee(deliveryTotalDTO));
            // 4.比较运费是否发生变化
            BigDecimal deliveryFee = deliveryFeeSnapshotResp.getDeliveryFee();
            if (deliveryFee.compareTo(refreshVO.getOriDeliveryFee()) == 0) {
                return refreshVO;
            }
            // 5.更新运费
            OrderResp updateOrderInfo = RpcResultUtil.handle(orderCommandProvider.refreshOrderAmount(RefreshOrderAmountReq.builder()
                .orderId(orderDTO.getId())
                .oriDeliveryFee(orderDTO.getDeliveryFee())
                .deliveryFeeSnapshotDTO(OrderDeliveryConvert.INSTANCE.convert2Dto(deliveryFeeSnapshotResp))
                .build()));

            // 6.build出参
            refreshVO.setRefreshFlag(true);
            refreshVO.setNewDeliveryFee(deliveryFee);
            refreshVO.setOrderAmount(updateOrderInfo.getPayablePrice());
            return refreshVO;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 从入参中找到需要刷新的三方仓订单
     * @param orderNoList
     * @return
     */
    private OrderAmountRefreshTotalDTO getRefreshOrder(List<String> orderNoList) {
        OrderAmountRefreshTotalDTO orderAmountRefreshTotalDTO = new OrderAmountRefreshTotalDTO();

        List<OrderResp> orderDTOList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNoList));
        if (CollectionUtils.isEmpty(orderDTOList)) {
            throw new ParamsException("未找到订单！");
        }

        // 一批下单拆单后多个订单的总金额，不包含运费
        BigDecimal mulOrderTotalPrice = orderDTOList.stream()
                .map(order -> NumberUtil.sub(order.getPayablePrice(), order.getDeliveryFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderAmountRefreshTotalDTO.setMulOrderTotalPrice(mulOrderTotalPrice);

        // 订单详情/列表页，单选一条订单，这条订单为组合包订单且非三方仓，查询关联的三方仓订单
        if (orderDTOList.size() == NumberConstant.ONE
            && !WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTOList.get(0).getWarehouseType())
            && OrderEnums.OrderType.COMBINE.getCode().equals(orderDTOList.get(0).getOrderType())) {
            List<OrderResp> combineOrderList = RpcResultUtil.handle(combineOrderQueryProvider.queryByCombineId(orderDTOList.get(0).getCombineOrderId(), orderDTOList.get(0).getTenantId()));
            OrderResp orderDTO = getRefreshThreeOrder(combineOrderList);
            if (orderDTO == null) {
                return null;
            }
            orderAmountRefreshTotalDTO.setOrderResp(orderDTO);
            return orderAmountRefreshTotalDTO;
        } else {
            // 普通订单，只查询三方仓订单
            OrderResp orderDTO = getRefreshThreeOrder(orderDTOList);
            if (orderDTO == null) {
                return null;
            }
            orderAmountRefreshTotalDTO.setOrderResp(orderDTO);
            return orderAmountRefreshTotalDTO;
        }
    }

    /**
     * 多条订单，筛选三方仓订单且不是预售订单
     * @param orderList
     * @return
     */
    private OrderResp getRefreshThreeOrder(List<OrderResp> orderList) {
        List<OrderResp> threeOrderList = orderList.stream()
            .filter(o -> WarehouseTypeEnum.THREE_PARTIES.getCode().equals(o.getWarehouseType()) && !OrderTypeEnum.PRESALE_ORDER.getValue().equals(o.getOrderType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(threeOrderList)) {
            return null;
        }
        OrderResp orderDTO = threeOrderList.get(0);
        if (!OrderStatusEnum.NO_PAYMENT.getCode().equals(orderDTO.getStatus())) {
            throw new BizException("只有待支付的订单才需要刷新金额！");
        }
        return orderDTO;
    }

    /**
     * 组合商品创建组合订单
     *
     * @param orderVOS
     */
    private void createCombineOrder(List<OrderVO> orderVOS) {
        // 判断是否是组合包商品下单
        OrderVO orderVO = orderVOS.get(NumberConstant.ZERO);
        if (OrderEnums.OrderType.COMBINE.getCode().equals(orderVO.getOrderType())) {
//            Long combineOrderId = combineOrderService.create(orderVO);
            Long combineOrderId = RpcResultUtil.handle(combineOrderCommandProvider.add(orderVO.getCombineItemId(), orderVO.getTenantId()));
            orderVOS.forEach(e -> e.setCombineOrderId(combineOrderId));
        }
    }


    /**
     * 检查是否有同一天配送日的三方仓订单 - 全鲜沐商品
     *
     * @return
     */
    private boolean checkSampleDayOrderOnlyXianmuItem(Order order) {
        // 查询是否有同一配送日的订单
        // 是否有已经有同一个配送日的订单
        OrderQueryDTO queryDTO = OrderQueryDTO.builder()
                .id(order.getId())
                .tenantId(order.getTenantId())
                .storeId(order.getStoreId())
                .deliveryTime(Objects.isNull(order.getDeliveryTime()) ? null : TimeUtils.convertTOLocalDate(order.getDeliveryTime()))
                .supplierTenantId(order.getSupplierTenantId())
                .build();
        List<OrderResp> orderList = querySameDeliveryOrders(queryDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }

        List<Long> orderIds = orderList.stream().map(OrderResp::getId).distinct().collect(Collectors.toList());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        List<OrderItemAndSnapshotResp> orderItemSnapshotList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
        Map<Long, List<OrderItemAndSnapshotResp>> orderItemMap = orderItemSnapshotList.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));

        for (Map.Entry<Long, List<OrderItemAndSnapshotResp>> entry : orderItemMap.entrySet()) {
            List<OrderItemAndSnapshotResp> tmpList = entry.getValue();
            boolean allMatchQuotationTypeFlag = tmpList.stream().allMatch(e -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType()));
            if (allMatchQuotationTypeFlag) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void saveSupplierDeliveryFee(Order order) {
        Long id = order.getId();
        log.info("开始处理订单id：{}供应商应收运费", id);

        // 查询品牌和供应商之间的运费规则
        TenantDeliveryFeeRule tenantDeliveryFeeRule = tenantDeliveryFeeRuleService.selectByTenantId(order.getTenantId());

        // boss后台配置运费规则 【随鲜沐商城规则】 判断是否有全鲜沐品订单
        if (TenantDeliveryEnum.TypeEnum.FOLLOW_SUPPLIER.getType().equals(tenantDeliveryFeeRule.getType())) {
            // 全鲜沐品订单
            if (checkSampleDayOrderOnlyXianmuItem(order)) {
                // 当日第二笔订单免费
                supplierDeliveryInfoService.saveSupplierDeliveryInfo(order.getOrderNo(), ZERO, null, TenantDeliveryFeeRuleTypeEnum.FOLLOW_SUPPLIER.getType());
                log.info("订单编号：{}已存在同一配送日全鲜沐品订单，无需重复收取运费", order.getOrderNo());
                return;
            }

        } else {
            // boss后台配置运费规则【免运费】【自定义】

            // 是否有已经有同一个配送日的订单
            OrderQueryDTO queryDTO = OrderQueryDTO.builder()
                    .id(id)
                    .tenantId(order.getTenantId())
                    .storeId(order.getStoreId())
                    .deliveryTime(Objects.isNull(order.getDeliveryTime()) ? null : TimeUtils.convertTOLocalDate(order.getDeliveryTime()))
                    .supplierTenantId(order.getSupplierTenantId())
                    .build();
            List<OrderResp> orderList = querySameDeliveryOrders(queryDTO);
            if (!CollectionUtils.isEmpty(orderList)) {
                supplierDeliveryInfoService.saveSupplierDeliveryInfo(order.getOrderNo(), ZERO, null, TenantDeliveryFeeRuleTypeEnum.FOLLOW_SUPPLIER.getType());
                log.info("订单编号：{}已存在同一配送日订单，无需重复收取运费", order.getOrderNo());
                return;
            }
        }

        // 参数集合
        List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        OrderDTO orderDTO = new OrderDTO();

        // 查询地址
        OrderAddressResp orderAddressDTO = RpcResultUtil.handle(orderAddressQueryProvider.queryByOrderId(order.getTenantId(), id));

        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(id));
        List<Long> orderItemIds = orderItemAndSnapshotDTOList.stream().map(OrderItemAndSnapshotResp::getOrderItemId).collect(Collectors.toList());
        List<OrderItemSnapshotResp> snapshotList = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemIds(orderItemIds));
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotMap = snapshotList.stream()
                .collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, item -> item));

        List<Long> skuIds = snapshotList.stream().map(OrderItemSnapshotResp::getSkuId).distinct().collect(Collectors.toList());
        List<ProductSkuDetailResp> productSkuDTOS = productQueryFacade.querySkuInfo(skuIds);
        Map<Long, ProductSkuDetailResp> productSkuDTOMap = productSkuDTOS.stream()
                .collect(Collectors.toMap(ProductSkuDetailResp::getSkuId, item -> item));

        for (OrderItemAndSnapshotResp orderItem : orderItemAndSnapshotDTOList) {
            OrderItemDTO itemDTO = new OrderItemDTO();
            OrderItemSnapshotResp itemSnapshot = orderItemSnapshotMap.get(orderItem.getOrderItemId());
            itemDTO.setSkuId(itemSnapshot.getSkuId());
            itemDTO.setSupplySkuId(itemSnapshot.getSupplierSkuId());
            itemDTO.setGoodsType(itemSnapshot.getGoodsType());
            // 货品类型为自营货品，取商城价
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(itemSnapshot.getGoodsType())) {
                itemDTO.setCalcPartDeliveryFee(orderItem.getTotalPrice());
                // 货品类型为报价货品，取报价价格
            } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(itemSnapshot.getGoodsType())) {
                itemDTO.setCalcPartDeliveryFee(NumberUtil.mul(itemSnapshot.getSupplyPrice(), orderItem.getAmount()));
            }
            itemDTO.setAmount(orderItem.getAmount());

            ProductSkuDetailResp productSkuDTO = productSkuDTOMap.get(itemSnapshot.getSkuId());
            itemDTO.setFirstCategoryId(productSkuDTO.getFirstCategoryId());
            itemDTO.setSku(productSkuDTO.getSku());
            itemDTO.setSubAgentType(productSkuDTO.getSubAgentType());
            itemDTO.setCategoryType(productSkuDTO.getCategoryType());
            orderItemDTOS.add(itemDTO);
        }

        // 订单信息参数
        orderDTO.setTenantId(order.getTenantId());
        orderDTO.setCity(orderAddressDTO.getCity());
        orderDTO.setArea(orderAddressDTO.getArea());
        OrderAddress orderAdress = new OrderAddress();
        BeanUtils.copyProperties(orderAddressDTO, orderAdress);
        orderDTO.setOrderAddress(orderAdress);
        orderDTO.setOrderItemDTOList(orderItemDTOS);


        DeliveryFeeStrategy deliveryFeeStrategy = DeliveryFeeStrategyFactory.getByType(tenantDeliveryFeeRule.getType());
        DeliveryFeeResultDTO deliveryFeeResultDTO = deliveryFeeStrategy.calculateDeliveryFee(orderDTO, tenantDeliveryFeeRule);

        // 保存运费以及运费规则
        supplierDeliveryInfoService.saveSupplierDeliveryInfo(order.getOrderNo(), deliveryFeeResultDTO.getDeliveryFee(), deliveryFeeResultDTO.getRule(),
                TenantDeliveryFeeRuleTypeEnum.FOLLOW_SUPPLIER.getType());
        log.info("供应商：{}收取订单编号：{}运费金额为：{}", order.getSupplierTenantId(), order.getOrderNo(), deliveryFeeResultDTO.getDeliveryFee());
    }

    @Override
    public void updateOrderByOutboundTaskCreate(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            log.error("订单orderNoList为空");
            return;
        }

        // 查询订单信息
//        List<Order> orderList = orderMapper.queryByOrderNos(orderNoList);
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNoList));
        if (CollectionUtils.isEmpty(orderList)) {
            log.error("订单为空");
            return;
        }

        List<String> orderNos = orderList.stream()
                .filter(e -> OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(e.getWarehouseType())
                        && OrderStatusEnum.WAITING_DELIVERY.getCode().equals(e.getStatus()))
                .map(OrderResp::getOrderNo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNos)) {
            log.warn("rocketmq 收到订单出库任务创建消息，三方仓订单为空，消息内容：{}", orderNoList);
            return;
        }
        // 三方仓 待出库更新为待收货
        OrderStatusBatchUpdateReq updateReq = new OrderStatusBatchUpdateReq();
//        updateReq.setStatus(OrderStatusEnum.DELIVERING.getCode());
        updateReq.setOrderNos(orderNos);
        updateReq.setOriginStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
        updateReq.setUpdateStatus(OrderStatusEnum.DELIVERING.getCode());
        Integer count = RpcResultUtil.handle(orderCommandProvider.batchUpdateStatus(updateReq));
//        Integer count = orderMapper.batchUpdateOrderStatusByIds(orderIds, OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.DELIVERING.getCode());
        log.info("rocketmq 收到订单出库任务创建消息，三方仓订单更新待收货数量，orderIds.size={}, updateSize={}", orderNos.size(), count);
    }

    /**
     * 冻结库存
     */
    private void localStock(OrderVO orderVO, MerchantAddressVO merchantAddressVO, LoginContextInfoDTO
            loginContextInfoDTO) {
        OrderDTO orderDTO = new OrderDTO();
        BeanUtils.copyProperties(orderVO, orderDTO);
        orderDTO.setId(orderVO.getOrderId());
        OrderAddress orderAddress = new OrderAddress();
        BeanUtils.copyProperties(merchantAddressVO, orderAddress);
        orderAddress.setContactName(merchantAddressVO.getName());
        orderAddress.setContactPhone(merchantAddressVO.getPhone());
        orderDTO.setOrderAddress(orderAddress);
        orderDTO.setTenantId(loginContextInfoDTO.getTenantId());
        orderDTO.setStoreId(loginContextInfoDTO.getStoreId());
        orderDTO.setAccountId(loginContextInfoDTO.getAccountId());
        orderDTO.setSupplyTenantId(orderVO.getSupplierTenantId());
        List<OrderItemVO> orderItemVOS = orderVO.getOrderItemVOS();
        orderDTO.setWarehouseType(orderVO.getWarehouseType());
        List<OrderItemDTO> orderItemDTOList = orderItemVOS.stream().map(orderItemVO -> {
            OrderItemDTO orderItemDTO = new OrderItemDTO();
            BeanUtils.copyProperties(orderItemVO, orderItemDTO);
            orderItemDTO.setAmount(orderItemVO.getAmount());
            orderItemDTO.setSupplySku(orderItemVO.getSupplySku());
            orderItemDTO.setSupplySkuId(orderItemVO.getSupplierSkuId());
            orderItemDTO.setSkuTenantId(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemVO.getGoodsType()) ? XianmuSupplyTenant.TENANT_ID : loginContextInfoDTO.getTenantId());

            return orderItemDTO;
        }).collect(Collectors.toList());
        orderDTO.setOrderItemDTOList(orderItemDTOList);
        stockService.lockStock(loginContextInfoDTO, orderDTO);
    }

    @Override
    public Integer countAgentInOrder(Long tenantId, Long orderId) {
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        return orderItemAndSnapshotList.stream()
                .filter(orderItemAndSnapshotDTO -> OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())
                        && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItemAndSnapshotDTO.getGoodsType()))
                .mapToInt(OrderItemAndSnapshotResp::getAmount)
                .sum();
    }

    private void createOrder(OrderVO orderVO, MerchantAddressVO merchantAddressVO, LoginContextInfoDTO
            loginContextInfoDTO) {
        List<OrderItemVO> orderItemVOS = orderVO.getOrderItemVOS();
        OrderCreateReq createReq = new OrderCreateReq();

        com.cosfo.ordercenter.client.resp.OrderDTO orderDTO = OrderConverter.INSTANCE.voToDTO(orderVO);
        orderDTO.setTenantId(loginContextInfoDTO.getTenantId());
        orderDTO.setStoreId(loginContextInfoDTO.getStoreId());
        orderDTO.setAccountId(loginContextInfoDTO.getAccountId());
        orderDTO.setSupplierTenantId(orderVO.getSupplierTenantId());
        orderDTO.setOrderNo(orderVO.getOrderNo());
        orderDTO.setWarehouseType(orderVO.getWarehouseType());
        orderDTO.setPayablePrice(orderVO.getPayablePrice());
        orderDTO.setDeliveryFee(orderVO.getDeliveryFee());
        orderDTO.setPayType(orderVO.getPayType());
        orderDTO.setRemark(orderVO.getRemark());
        orderDTO.setWarehouseNo(orderVO.getWarehouseNo() == null ? null : String.valueOf(orderVO.getWarehouseNo()));
        orderDTO.setOrderType(orderVO.getOrderType());
        // 组合订单号
        orderDTO.setCombineOrderId(orderVO.getCombineOrderId());
        // 查询售后规则
        // 无仓订单是待支付
        if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderVO.getWarehouseType())) {
            orderDTO.setStatus(OrderStatusEnum.NO_PAYMENT.getCode());
        } else {
            orderDTO.setStatus(OrderStatusEnum.CREATING_ORDER.getCode());
        }
        // 标识新下单链路 1
        orderDTO.setOrderVersion(1);

        orderDTO.setApplyEndTime(orderVO.getApplyEndTime());
        orderDTO.setAutoFinishedTime(orderVO.getAutoFinishedTime());
        createReq.setOrderDTO(orderDTO);
        createReq.setDeliveryFeeSnapshotDTO(orderVO.getDeliveryFeeSnapshotVO());
        // 生成订单项
        List<OrderItemCreateReq> itemCreateList = orderItemVOS.stream().map(orderItemVO -> {
            OrderItemCreateReq orderItemCreateReq = OrderItemConverter.INSTANCE.voToReq(orderItemVO);
            OrderItemAfterSaleRuleDTO orderAfterSaleRule = orderItemVO.getOrderAfterSaleRule();
            if (orderAfterSaleRule != null) {
                OrderItemAfterSaleRuleReq orderItemAfterSaleRuleReq = new OrderItemAfterSaleRuleReq();
                if(FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(orderDTO.getFulfillmentType())) {
                    orderItemAfterSaleRuleReq.setApplyEndTime(orderAfterSaleRule.getExpressOrderApplyEndTime());
                }else{
                    orderItemAfterSaleRuleReq.setApplyEndTime(orderAfterSaleRule.getApplyEndTime());
                }
                orderItemAfterSaleRuleReq.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
                orderItemCreateReq.setOrderAfterSaleRule(orderItemAfterSaleRuleReq);
            }

            OrderCombineItemVO orderCombineItemVO = orderItemVO.getOrderCombineItemVO();
            if (orderCombineItemVO != null) {
                OrderCombineItemCreateReq combineItemCreateReq = new OrderCombineItemCreateReq();
                combineItemCreateReq.setMarketItemId(orderCombineItemVO.getMarketItemId());
                combineItemCreateReq.setQuantity(orderCombineItemVO.getQuantity());
                combineItemCreateReq.setOriginalPrice(orderCombineItemVO.getOriginalPrice());
                orderItemCreateReq.setOrderCombineItemCreateReq(combineItemCreateReq);
            }
            return orderItemCreateReq;
        }).collect(Collectors.toList());
        createReq.setOrderItemList(itemCreateList);
        // 生成订单地址信息
        com.cosfo.ordercenter.client.resp.OrderAddressDTO orderAddress = OrderAddressConverter.INSTANCE.toAddressDTO(merchantAddressVO);
        orderAddress.setTenantId(loginContextInfoDTO.getTenantId());
        createReq.setOrderAddressDTO(orderAddress);
        orderVO.setTenantId(loginContextInfoDTO.getTenantId());
        Long orderId = RpcResultUtil.handle(orderCommandProvider.create(createReq));
        orderVO.setOrderId(orderId);

        // 不影响后续发送消息，所以将快照信息删除
        orderVO.setDeliveryFeeSnapshotVO(null);
    }

    /**
     * 获取申请售后规则
     *
     * @param orderAfterSaleRuleVOS 售后规则
     * @param classificationId      商品分组Id
     * @param deliveryType          仓库类型 0无仓 1三方 2自营
     */
    private OrderItemAfterSaleRuleDTO getOrderAfterSaleRule(List<OrderAfterSaleRuleResp> orderAfterSaleRuleVOS,
                                                            Long classificationId, Integer deliveryType) {
        // 默认运费规则
        OrderAfterSaleRuleResp defaultOrderAfterSaleRuleVO = null;
        // 非默认
        OrderAfterSaleRuleResp orderAfterSaleRuleVO = null;

        for (OrderAfterSaleRuleResp afterSaleRuleVO : orderAfterSaleRuleVOS) {
            // 默认运费规则
            if (OrderAfterSaleRuleEnums.DefaultFlag.TRUE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                defaultOrderAfterSaleRuleVO = afterSaleRuleVO;
            } else if (OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                orderAfterSaleRuleVO = afterSaleRuleVO;
            }
        }
        if (Objects.isNull(defaultOrderAfterSaleRuleVO)) {
            throw new DefaultServiceException("默认售后规则为空，请检查租户默认售后规则配置");
        }
        String rule = defaultOrderAfterSaleRuleVO.getRule();
        OrderAfterSaleRuleDetailVO defaultOrderAfterSaleRuleDetailVO = JSONObject.parseObject(rule, OrderAfterSaleRuleDetailVO.class);
        // 可申请售后时间
        Integer applyEndTime = defaultOrderAfterSaleRuleDetailVO.getApplyEndTime();
        Integer expressOrderApplyEndTime = defaultOrderAfterSaleRuleDetailVO.getExpressOrderApplyEndTime();
        // 自动完成时间
        Integer autoFinishedTime = defaultOrderAfterSaleRuleDetailVO.getAutoFinishedTime();
        if (Objects.nonNull(orderAfterSaleRuleVO)) {
            OrderAfterSaleRuleEnums.Type type = OrderAfterSaleRuleEnums.Type.getByCode(orderAfterSaleRuleVO.getType());
            List<OrderAfterSaleRuleDetailVO> orderAfterSaleRuleDetailVOS = JSONObject
                    .parseArray(orderAfterSaleRuleVO.getRule(), OrderAfterSaleRuleDetailVO.class);
            List<OrderAfterSaleRuleDetailVO> matchOrderAfterSaleRuleDetailVos = new ArrayList<>();
            switch (type) {
                // 仓
                case WAREHOUSE:
                    matchOrderAfterSaleRuleDetailVos = orderAfterSaleRuleDetailVOS.stream()
                            .filter(orderAfterSaleRuleDetailVO -> deliveryType.equals(orderAfterSaleRuleDetailVO.getDeliveryType())).collect(Collectors.toList());
                    break;
                // 商品分组
                case CLASSIFICATION:
                    matchOrderAfterSaleRuleDetailVos = orderAfterSaleRuleDetailVOS.stream()
                            .filter(orderAfterSaleRuleDetailVO -> orderAfterSaleRuleDetailVO.getClassificationIds().contains(classificationId)).collect(Collectors.toList());
                    break;
                case NULL_ERROR:
                    break;
            }

            if (!CollectionUtils.isEmpty(matchOrderAfterSaleRuleDetailVos)) {
                OrderAfterSaleRuleDetailVO orderAfterSaleRuleDetailVO = matchOrderAfterSaleRuleDetailVos
                        .get(NumberConstant.ZERO);
                // 可申请售后时间
                applyEndTime = orderAfterSaleRuleDetailVO.getApplyEndTime();
                // 自动完成时间
                autoFinishedTime = orderAfterSaleRuleDetailVO.getAutoFinishedTime();
                expressOrderApplyEndTime = orderAfterSaleRuleDetailVO.getExpressOrderApplyEndTime();
            }
        }

        if(expressOrderApplyEndTime == null){
            expressOrderApplyEndTime = applyEndTime;
        }

        OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = new OrderItemAfterSaleRuleDTO();
        orderItemAfterSaleRuleDTO.setApplyEndTime(applyEndTime);
        orderItemAfterSaleRuleDTO.setAutoFinishedTime(autoFinishedTime);
        orderItemAfterSaleRuleDTO.setExpressOrderApplyEndTime(expressOrderApplyEndTime);
        return orderItemAfterSaleRuleDTO;
    }

    @Override
    public PageResultDTO<List<OrderVO>> list(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询订单数量
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setPageNum(orderQueryDTO.getPageNum());
        orderQueryReq.setPageSize(orderQueryDTO.getPageSize());
        if (orderQueryDTO.getStatus() != null && orderQueryDTO.getStatus().equals(OrderStatusEnum.WAIT_DELIVERY.getCode())) {
            orderQueryReq.setStatusList(Lists.newArrayList(
                    OrderStatusEnum.WAIT_DELIVERY.getCode(),
                    OrderStatusEnum.DELIVERING.getCode(),
                    OrderStatusEnum.WAITING_DELIVERY.getCode(),
                    OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(),
                    OrderStatusEnum.OUT_OF_STORAGE.getCode(),
                    OrderStatusEnum.WAIT_AUDIT.getCode()
            ));
        } else {
            orderQueryReq.setStatus(orderQueryDTO.getStatus());
        }
        orderQueryReq.setTenantId(loginContextInfoDTO.getTenantId());
        orderQueryReq.setStoreIds(Lists.newArrayList(loginContextInfoDTO.getStoreId()));
        PageInfo<OrderResp> orderPage = RpcResultUtil.handle(orderQueryProvider.queryOrderPage(orderQueryReq));
        if (orderPage == null || CollectionUtils.isEmpty(orderPage.getList())) {
            return new PageResultDTO<>(ResultDTOEnum.SUCCESS, new ArrayList<>(), orderQueryDTO.getPageNum(), orderQueryDTO.getPageSize(), 0L, 0);
        }
        //查询订单是否有未处理售后单
        List<OrderResp> orderList = orderPage.getList();
        List<Long> orderIds = orderList.stream().map(OrderResp::getId).collect(Collectors.toList());
        Map<Long, Integer> afterSaleCntMap = getAfterSaleCountMap(loginContextInfoDTO, orderIds);
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
        Map<Long, List<OrderItemAndSnapshotResp>> snapshotMap = orderItemAndSnapshotList.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));
        List<OrderVO> orderVOS = orderList.stream().map(order -> {
            OrderVO orderVO = OrderConverter.INSTANCE.dtoToVO(order);
            orderVO.setOrderId(order.getId());
            orderVO.setOrderTime(order.getCreateTime());
            // 支付和出库中都是待配送
            if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(order.getStatus())) {
                orderVO.setStatus(OrderStatusEnum.WAIT_DELIVERY.getCode());
            }

            // 查询供应商信息
            List<Long> supplierTenantIds = Collections.singletonList(order.getSupplierTenantId());
            List<Tenant> tenants = tenantService.querySupplierInfoBySupplierTenantIds(supplierTenantIds);
            OrderEnums.WarehouseTypeEnum warehouseTypeEnum = OrderEnums.WarehouseTypeEnum
                    .getByCode(order.getWarehouseType());
            orderVO.setSupplierName(warehouseTypeEnum.getName());
            // 查询商品信息
            List<OrderItemVO> orderItemVOS = OrderItemConverter.INSTANCE.dtoToVOList(snapshotMap.get(order.getId()));
            orderVO.setOrderItemVOS(orderItemVOS);
            // 统计件数信息
            AtomicReference<Integer> totalAmount = new AtomicReference<>(0);
            orderItemVOS.stream().forEach(orderItemVO -> {
                totalAmount.updateAndGet(v -> v + orderItemVO.getAmount());
            });
            orderVO.setSaleAfterFlag(afterSaleCntMap.getOrDefault(order.getId(), 0) != 0);
            orderVO.setTotalAmount(totalAmount.get());
            return orderVO;
        }).collect(Collectors.toList());
        // 计算非现金支付可用以及不可用金额
        calculateUsableNonCashAmount(orderVOS);
        return new PageResultDTO<>(ResultDTOEnum.SUCCESS, orderVOS, orderPage.getPageNum(), orderPage.getPageSize(), orderPage.getTotal(), orderPage.getPages());
    }

    @Override
    public ResultDTO<OrderVO> detail(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        OrderVO orderVO = OrderConverter.INSTANCE.dtoToVO(orderDTO);

        AssertCheckParams.notNull(orderVO, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");

        // 支付和出库中都是待配送
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderVO.getStatus())) {
            orderVO.setStatus(OrderStatusEnum.WAIT_DELIVERY.getCode());
        }

        // 是否强制计划单-铺货单
        boolean isForcePlanOrder = false;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(orderVO.getPlanOrderNo())) {
            PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(orderVO.getPlanOrderNo()));
            isForcePlanOrder = planOrderDetailResp != null && AgentOrderEnum.PlanTypeEnum.CREATE_FORCE_PLAN_ORDER.name().equals(planOrderDetailResp.getPlanType());
        }


        orderVO.setOrderId(orderId);

        // 设置仓信息
        OrderEnums.WarehouseTypeEnum warehouseTypeEnum = OrderEnums.WarehouseTypeEnum.getByCode(orderVO.getWarehouseType());
        orderVO.setSupplierName(Optional.ofNullable(warehouseTypeEnum).map(OrderEnums.WarehouseTypeEnum::getName).orElse(""));

        // 获取下单账号信息
        MerchantStoreAccountVO merchantStoreAccountVO = merchantStoreAccountService
                .queryAccountInfo(orderVO.getAccountId(), loginContextInfoDTO.getTenantId());
        StringBuffer accountName = new StringBuffer(merchantStoreAccountVO.getAccountName())
                .append("(").append(merchantStoreAccountVO.getPhone()).append(")");
        orderVO.setAccountName(accountName.toString());
        orderVO.setStoreName(loginContextInfoDTO.getStoreName());

        OrderAddressResp orderAddressDTO = RpcResultUtil.handle(orderAddressQueryProvider.queryByOrderId(loginContextInfoDTO.getTenantId(), orderId));
        orderVO.setOrderAddressDTO(orderAddressDTO);
        // 获取订单项信息
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));

        Map<Long, Integer> afterSaleCountMap = getAfterSaleCountMap(loginContextInfoDTO, Lists.newArrayList(orderId));
        orderVO.setSaleAfterFlag(afterSaleCountMap.getOrDefault(orderId, 0) != 0);

        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderId));
        afterSaleQueryReq.setTenantId(orderVO.getTenantId());
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));
        Map<Long, List<OrderAfterSaleResp>> afterSaleMap = afterSaleDTOList.stream()
                .collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
        OrderAfterSaleEnableApplyReq afterSaleEnableApplyReq = new OrderAfterSaleEnableApplyReq();
        afterSaleEnableApplyReq.setOrderId(orderId);
        afterSaleEnableApplyReq.setTenantId(loginContextInfoDTO.getTenantId());
        Map<Long, OrderAfterSaleEnableResp> applyMap = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryEnableApply(afterSaleEnableApplyReq));
        if (CollectionUtils.isEmpty(applyMap)) {
            log.error("订单没有找到可售后信息：{}", orderId, new Exception());
        }
        List<OrderItemVO> orderItemVOS = OrderItemConverter.INSTANCE.dtoToVOList(orderItemAndSnapshotDTOList);
        for (OrderItemVO orderItemVO : orderItemVOS) {
            OrderAfterSaleEnableResp enableApplyDTO = applyMap.get(orderItemVO.getId());
            if (Objects.isNull(enableApplyDTO)) {
                log.error("订单项没有找到可售后信息：{}，order：{}", orderItemVO.getId(), orderId, new Exception());
                continue;
            }

            // 查询已经在售后的数量 去除掉换货以及补发成功的数量
            boolean flag = Objects.nonNull(enableApplyDTO) && OrderStatusEnum.ableApplyAfterSale(orderVO.getStatus())
                    && (enableApplyDTO.getEnableApplyAmount() > 0 || enableApplyDTO.getEnableApplyQuantity() > 0);
            // 判断售后到期时间
            LocalDateTime now = LocalDateTime.now();
            if (flag && orderItemVO.getAfterSaleExpiryTime() != null) {
                flag = orderItemVO.getAfterSaleExpiryTime().compareTo(now) > NumberConstant.ZERO;
            }

            // 如果是强制计划单-铺货单，不能发起配送前退款和配送后售后
            if(isForcePlanOrder){
                flag = false;
            }

            OrderAfterSaleFlagEnum orderAfterSaleFlagEnum = OrderAfterSaleFlagEnum.get(orderVO.getStatus(), afterSaleMap.get(orderItemVO.getId()), flag);
            orderItemVO.setAfterSaleFlag(orderAfterSaleFlagEnum.getCode());
            orderItemVO.setEnableApplyAfterSale(flag);
            orderItemVO.setNeedSendAmount(enableApplyDTO.getEnableApplyAmount());
        }

        orderVO.setOrderItemVOS(orderItemVOS);

        // 查询订单操作日志
        buildOrderBizLog(orderVO);
        //  查询payment
        buildPaymentInfo(orderVO);
        // 计算非现金支付可用以及不可用金额
        calculateUsableNonCashAmount(Collections.singletonList(orderVO));
        return ResultDTO.success(orderVO);
    }


    private void buildPaymentInfo(OrderVO orderVO) {
        PaymentItem paymentItem = paymentItemMapper.selectByOrderId(orderVO.getTenantId(), orderVO.getOrderId());
        if (paymentItem == null) {
            return;
        }

        orderVO.setPaymentReceipt(paymentItem.getPaymentReceipt());

        Payment payment = paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
        if (payment == null) {
            return;
        }

        if (TradeTypeEnum.COMBINED_PAY.getDesc().equals(payment.getTradeType())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.selectByCombinedPaymentNo(payment.getPaymentNo());
            if (CollectionUtils.isEmpty(combinedDetails)) {
                return;
            }
            Map<Long, String> tradeTypeMap = combinedDetails.stream().collect(Collectors.toMap(PaymentCombinedDetail::getId, PaymentCombinedDetail::getTradeType));
            List<Long> combinedDetailIds = combinedDetails.stream().map(PaymentCombinedDetail::getId).collect(Collectors.toList());
            List<PaymentCombinedOrderDetail> paymentCombinedOrderDetails = paymentCombinedOrderDetailService.selectByCombinedDetailIds(orderVO.getTenantId(), combinedDetailIds);
            if (CollectionUtils.isEmpty(paymentCombinedOrderDetails)) {
                return;
            }
            List<PaymentCombinedDetailVO> detailVOList = paymentCombinedOrderDetails.stream()
                    .filter(el -> Objects.equals(el.getOrderId(), orderVO.getOrderId()))
                    .map(detail -> {
                        PaymentCombinedDetailVO vo = new PaymentCombinedDetailVO();
                        vo.setPayType(TradeTypeEnum.getPayTypeByTradeType(tradeTypeMap.get(detail.getCombinedDetailId())));
                        vo.setPayAmount(detail.getTotalPrice());
                        return vo;
                    })
                    .collect(Collectors.toList());
            orderVO.setPaymentCombinedDetail(detailVOList);
        }
    }

    private void buildOrderBizLog(OrderVO orderVO){
        try {
            QueryBizLogReq queryDTO = new QueryBizLogReq();
            queryDTO.setTenantId(orderVO.getTenantId());
            queryDTO.setBizDomain("订单");
            queryDTO.setEntityType("订单操作记录");
            queryDTO.setBizKey(orderVO.getOrderNo());
            queryDTO.setPageIndex(1);
            queryDTO.setPageSize(100);
            PageInfo<BizLogListResp> bizLogListVOPageInfo = bizLogFacade.listBizLog(queryDTO);
            List<BizLogListResp> bizLogListVOList = bizLogListVOPageInfo.getList();
            orderVO.setBizLogListRespList(bizLogListVOList);
        } catch (Exception e) {
            log.warn("订单操作记录异常，orderNo={}", orderVO.getOrderNo(), e);
        }
    }


    private Map<Long, Integer> getAfterSaleCountMap(LoginContextInfoDTO loginContextInfoDTO, List<Long> orderIds) {
        OrderAfterSaleCountReq countReq = new OrderAfterSaleCountReq();
        countReq.setOrderIds(orderIds);
        countReq.setTenantId(loginContextInfoDTO.getTenantId());
        countReq.setStatusList(Lists.newArrayList(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.UNAUDITED.getValue()));
        Map<Long, Integer> afterSaleCntMap = RpcResultUtil.handle(orderAfterSaleQueryProvider.countOrderAfterSaleByOrderId(countReq));
        return afterSaleCntMap;
    }

    /**
     * 确认收货
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    @Override
    public Boolean confirm(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        AssertCheckParams.notNull(orderDTO, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不正确");
        // 校验订单信息
        if (orderDTO.getStatus().equals(OrderStatusEnum.DELIVERING.getCode())
                || orderDTO.getStatus().equals(OrderStatusEnum.WAITING_DELIVERY.getCode())) {
            // 更新订单状态
            OrderConfirmReq orderConfirmReq = new OrderConfirmReq();
            orderConfirmReq.setOrderId(orderId);
            orderConfirmReq.setTenantId(loginContextInfoDTO.getTenantId());
            Boolean result = RpcResultUtil.handle(orderCommandProvider.confirm(orderConfirmReq));
            return result;
        }

        return false;
    }

    /**
     * 判断一笔订单是否可以被取消；
     * 只有下单中，或者待支付的订单可以取消；
     *
     * @param order
     * @return
     */
    private boolean isOrderCanBeCancel(OrderResp order) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.fromCode(order.getStatus());
        switch (orderStatusEnum) {
            case CREATING_ORDER:
            case NO_PAYMENT:
                return true;
            default:
                return false;
        }
    }

    /**
     * 取消订单
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    @Override
    public Boolean cancel(Long orderId, LoginContextInfoDTO loginContextInfoDTO, Integer cancelType) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单ID不能为空");
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        AssertCheckParams.notNull(orderDTO, ResultDTOEnum.NOT_FOUND.getCode(), "未查询到订单信息");

        // 消息的接口也会走这里，设置下上下文参数
        loginContextInfoDTO = setLoginContextInfoDTO(orderDTO, loginContextInfoDTO);

        // 只有下单中，或者待支付的订单可以取消；
        if (!isOrderCanBeCancel(orderDTO)) {
            // 如果订单不可取消，那么直接返回成功；
            log.warn("订单不可取消:{}", JSON.toJSONString(orderDTO));
            return false;
        }

        List<OrderResp> orderList = new ArrayList<>();

        // 判断订单是否是组合订单并且不是超时取消
        if (OrderEnums.OrderType.COMBINE.getCode().equals(orderDTO.getOrderType()) && Objects.equals(cancelType, OrderCancelTypeEnum.MANUALLY_CANCEL.getType())) {
            orderList = RpcResultUtil.handle(combineOrderQueryProvider.queryByCombineId(orderDTO.getCombineOrderId(), orderDTO.getTenantId()));
        } else {
            orderList.add(orderDTO);
        }

        // 处理支付单
        Long tenantId = loginContextInfoDTO.getTenantId();
        Pair<Boolean, Integer> paymentCancelPair = processPaymentStatus(tenantId, orderId);
        Boolean paymentCancelFlag = paymentCancelPair.getKey();
        Integer paymentStatus = paymentCancelPair.getValue();
        if (!paymentCancelFlag) {
            // 如果是用户手动取消则告知暂时不能取消，如果是延时消息则再次投递
//            if (Objects.equals(cancelType, OrderCancelTypeEnum.MANUALLY_CANCEL.getType())) {
//                throw new BizException("订单正在支付，暂时不支持取消");
//            }
            if (Objects.equals(paymentStatus, PaymentEnum.Status.SUCCESS.getCode())) {
                log.info("订单：[{}]，支付成功，取消流程结束", orderId);
                return false;
            }
//            sendOrderCancelMessage(orderId, OrderCancelTypeEnum.TIME_OUT_CANCEL.getType(), businessTimeConfig.getOrderCancelTimeOut());
            return false;
        }

        // 批量取消订单
        List<Long> orderIds = orderList.stream().map(OrderResp::getId).collect(Collectors.toList());
        Boolean enableCancel = orderService.batchCancelOrder(tenantId, orderIds);

        // 释放库存
        if (enableCancel) {
            releaseStock(orderList, loginContextInfoDTO);
            releaseAmount(tenantId, orderId);
        }
        log.info("订单：[{}]取消完毕", orderIds);
        return true;
    }

    /**
     * 释放冻结金额
     * 组合支付中的余额、非现金支付，会先被冻结，为实际支付则会释放
     *
     * @param orderId
     */
    @Override
    public void releaseAmount(Long tenantId, Long orderId) {
        log.info("开始处理释放冻结金额, orderId: {}", orderId);
        // 查询该订单的所有组合支付记录，而不是只查最新的
        List<PaymentItem> allPaymentItems = paymentItemMapper.selectAllByOrderId(tenantId, orderId);
        if (CollectionUtils.isEmpty(allPaymentItems)) {
            log.info("未查询到支付单信息, orderId:{}", orderId);
            return;
        }

        for (PaymentItem paymentItem : allPaymentItems) {
            Long paymentId = paymentItem.getPaymentId();
            Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
            if (payment == null) {
                log.warn("未查询到支付单信息, paymentId:{}", paymentId);
                continue;
            }

            if (!TradeTypeEnum.COMBINED_PAY.getDesc().equals(payment.getTradeType())) {
                log.info("支付单非组合支付, paymentId:{}", paymentId);
                continue;
            }

            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.selectByCombinedPaymentNo(payment.getPaymentNo());
            if (CollectionUtils.isEmpty(combinedDetails)) {
                log.info("未查询到组合支付明细, paymentId:{}", paymentId);
                continue;
            }
            combinedDetails.stream()
                    .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                    .filter(this::needRelease)
                    .forEach(el -> {
                        paymentCombinedDetailService.releaseFreezeAmount(el);
                    });
        }
        log.info("处理释放冻结金额结束, orderId: {}", orderId);
    }

    /**
     * 检查组合支付明细是否需要释放冻结金额
     *
     * @param detail 组合支付明细
     * @return true-需要释放，false-不需要释放
     */
    private boolean needRelease(PaymentCombinedDetail detail) {
        // 只有状态为冻结中(6)的记录才需要释放
        return PaymentEnum.Status.FREEZE.getCode().equals(detail.getStatus());
    }

    private LoginContextInfoDTO setLoginContextInfoDTO(OrderResp orderDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (loginContextInfoDTO == null) {
            loginContextInfoDTO = new LoginContextInfoDTO();
            loginContextInfoDTO.setTenantId(orderDTO.getTenantId());
            loginContextInfoDTO.setStoreId(orderDTO.getStoreId());
            loginContextInfoDTO.setAccountId(orderDTO.getAccountId());
        }
        return loginContextInfoDTO;
    }

    public void releaseStock(List<OrderResp> orderList, LoginContextInfoDTO loginContextInfoDTO) {
        Set<Long> orderIdSet = orderList.stream().map(OrderResp::getId).collect(Collectors.toSet());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(Lists.newArrayList(orderIdSet));
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
        List<OrderItemDTO> itemDTOList = OrderItemConverter.INSTANCE.snapshotToItemDTOList(orderItemAndSnapshotDTOList);
//        List<OrderItemDTO> itemDTOList = orderItemMapper.queryOrderItemVOByOrderIds(loginContextInfoDTO.getTenantId(),
//                new ArrayList<>(orderIdSet));
        Map<Long, List<OrderItemDTO>> orderItemDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(itemDTOList)) {
            orderItemDTOMap = itemDTOList.stream().collect(Collectors.groupingBy(OrderItemDTO::getOrderId));
        }

        for (OrderResp order : orderList) {
            OrderDTO orderDto = new OrderDTO();
            BeanUtils.copyProperties(order, orderDto);
            // 查询所有订单项
            List<OrderItemDTO> itemDTOS = orderItemDTOMap.get(order.getId());
            orderDto.setOrderItemDTOList(itemDTOS);
            stockService.unlockStockByCancel(loginContextInfoDTO.getTenantId(), orderDto);
        }
    }

    @Override
    public Boolean batchCancelOrder(Long tenantId, List<Long> orderIds) {
        OrderCancelReq orderCancelReq = new OrderCancelReq();
        orderCancelReq.setOrderIds(orderIds);
        orderCancelReq.setTenantId(tenantId);
        return RpcResultUtil.handle(orderCommandProvider.cancel(orderCancelReq));
    }

    /**
     * 处理支付单的状态
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    public Pair<Boolean, Integer> processPaymentStatus(Long tenantId, Long orderId) {
        // 查询最新的支付单
        PaymentItem paymentItem = paymentItemMapper.selectByOrderId(tenantId, orderId);
        if (paymentItem == null) {
            log.info("orderId：[{}]暂无支付单，订单取消-处理支付单状态流程结束", orderId);
            return Pair.of(Boolean.TRUE, null);
        }
        Long paymentId = paymentItem.getPaymentId();
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            log.info("orderId：[{}]暂无支付单，订单取消-处理支付单状态流程结束", orderId);
            return Pair.of(Boolean.TRUE, null);
        }
        // 只有本地支付方式才校验本地状态、三方支付方式查询外部接口
        if (com.cosfo.mall.common.constants.TradeTypeEnum.getNativeTradeType().contains(payment.getTradeType()) && Objects.equals(payment.getStatus(), PaymentEnum.Status.DEALING.getCode())) {
            log.info("orderId：[{}]，支付单id：[{}], 正在支付中，不可取消", orderId, payment.getStatus());
            return Pair.of(Boolean.TRUE, PaymentEnum.Status.DEALING.getCode());
        }

        // 若插件支付，这里可能找不到预下单的信息，可能会出现订单取消，支付成功的情况
        if (com.cosfo.mall.common.constants.TradeTypeEnum.getExternalTradeType().contains(payment.getTradeType())) {
            PaymentResult paymentResult = paymentService.queryExternalPaymentStatus(payment);
            if (paymentResult == null) {
                log.info("订单：[{}]，支付单：[{}]结果都查不到，无需等待", orderId, paymentId);
                return Pair.of(Boolean.TRUE, null);
            }
            OrderPayResultDTO orderPayResultDTO = paymentResult.getOrderPayResultDTO();
            if (orderPayResultDTO == null) {
                log.error("订单：[{}]，支付单：[{}]查询不到对应的支付单状态", orderId, paymentId, new ProviderException("查询不到对应外部支付单的状态"));
                return Pair.of(Boolean.TRUE, PaymentEnum.Status.DEALING.getCode());
            }
            Integer paymentStatus = orderPayResultDTO.getPaymentStatus();
            if (PaymentEnum.Status.SUCCESS.getCode().equals(paymentStatus)) {
                log.info("订单：[{}]，支付单：[{}]，支付成功，不可取消", orderId, paymentId);
                return Pair.of(Boolean.FALSE, PaymentEnum.Status.SUCCESS.getCode());
            }
            if (PaymentEnum.Status.DEALING.getCode().equals(paymentStatus)) {
                log.info("订单：[{}]，支付单：[{}]，正在支付中", orderId, paymentId);
                // 如果是超时延时关单，需要取消对应的支付单
//                if (!Objects.equals(cancelType, OrderCancelTypeEnum.TIME_OUT_CANCEL.getType())) {
//                    return Pair.of(Boolean.FALSE, PaymentEnum.Status.DEALING.getCode());
//                }
                // 尝试取消对应的支付单
                try {
                    // 交易不存在 取消必定会失败 放行可取消
                    if (Objects.equals(orderPayResultDTO.getBank_code(), HuiFuApi.TRADE_NOT_FOUND_BANK_CODE)) {
                        log.error("订单id：[{}]，支付单：[{}]，交易不存在，结束关单", orderId, paymentId);
                        return Pair.of(Boolean.TRUE, PaymentEnum.Status.DEALING.getCode());
                    }
                    log.info("尝试关闭支付单：[{}]", paymentId);
                    paymentService.closePaymentOrder(payment);
                } catch (Exception e) {
                    log.warn("订单：[{}]，支付单：[{}]，关闭失败", orderId, paymentId, e);
                    return Pair.of(Boolean.TRUE, PaymentEnum.Status.DEALING.getCode());
                }
            }
        }
        return Pair.of(Boolean.TRUE, null);
    }


//    private Boolean checkOrderPayResult(List<Payment> payments) {
//        for (Payment payment : payments) {
//            if (Objects.isNull(payment)) {
//                continue;
//            }
//            Integer onlinePayChannel = payment.getOnlinePayChannel();
//            if (Objects.isNull(onlinePayChannel)) {
//                continue;
//            }
//            if (Objects.isNull(payment.getTradeType())) {
//                continue;
//            }
//            if (Objects.equals(payment.getTradeType(), TradeTypeEnum.BILL.getDesc()) || Objects.equals(payment.getTradeType(), TradeTypeEnum.BALANCE.getDesc())) {
//                continue;
//            }
//            PayTemplate payTemplate = payStrategyFactory.getTemplateByTradeType(payment.getTradeType());
//            PaymentRequest request = PaymentRequest.builder().paymentId(payment.getId()).tenantId(payment.getTenantId()).build();
//            PaymentResult paymentResult = payTemplate.queryLastPaymentResult(request);
//            OrderPayResultDTO orderPayResultDTO = paymentResult.getOrderPayResultDTO();
//            if (Objects.nonNull(orderPayResultDTO)) {
//                if (PaymentEnum.Status.SUCCESS.getCode().equals(orderPayResultDTO.getPaymentStatus())) {
//                    log.info("订单{} 支付成功", orderPayResultDTO.getOrderId());
//                    return Boolean.FALSE;
//                }
//            }
//        }
//
//
//        return Boolean.TRUE;
//    }

    @Override
    public List<OrderVO> queryOrderStatus(List<String> orderNos) {
        AssertCheckParams.isTrue(!CollectionUtils.isEmpty(orderNos), ResultDTOEnum.PARAMETER_MISSING.getCode(),
                "订单编号不能为空");
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        boolean allMatch = orderList.stream()
                .allMatch(orderDTO -> orderDTO.getStatus().equals(OrderStatusEnum.NO_PAYMENT.getCode()));

        if (allMatch) {
            log.info("线程{}：{}订单状态返回时间执行时间：{}ms", Thread.currentThread().getName(), JSONObject.toJSONString(orderNos),
                    System.currentTimeMillis());
        }
        return OrderConverter.INSTANCE.dtoToVO(orderList);
    }


    @Override
    public void dealFulfillmentOrderCreate(FulfillmentOrderResultMessageDTO dto) {
        String orderNo = dto.getOrderNo();
        // 查询订单
        OrderResp orderResp = RpcResultUtil.handle(orderQueryProvider.queryByNo(orderNo));
        if (orderResp == null) {
            BizException bizException = new BizException("订单不存在,重试");
            log.error("履约单创建成功消息，订单{}未查询到", orderNo, bizException);
            throw bizException;
        }

        FulfillmentInfo fulfillmentInfo = dto.getFulfillmentInfo();
        if (fulfillmentInfo == null || fulfillmentInfo.getFulfillmentNo() == null) {
            String errmsg = String.format("履约单创建成功消息，订单%s，履约单号为空", orderNo);
            log.error(errmsg, new BizException(errmsg));
            return;
        }

        // 待审核期间，发起了改单，ofc也会创建履约单，发送履约单创建成功消息（带fulfillmentNo）
        if (OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderResp.getStatus())
                || OrderStatusEnum.WAIT_DELIVERY.getCode().equals(orderResp.getStatus())
                || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderResp.getStatus())) {
            log.info("履约单创建成功，更新订单:{} 履约单号、配送时间和城配仓", orderNo);
            OrderFulfillmentOrderCreateReq req = new OrderFulfillmentOrderCreateReq();
            req.setOrderId(orderResp.getId());
            req.setFulfillmentNo(fulfillmentInfo.getFulfillmentNo());
            req.setDeliveryTime(Optional.ofNullable(fulfillmentInfo.getFulfillmentDate()).map(e -> e.atStartOfDay()).orElse(null));
            req.setStoreNo(fulfillmentInfo.getStoreNo());
            RpcResultUtil.handle(orderCommandProvider.fulfillmentOrderCreate(req));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updatePaySuccess(Long orderId) {
        OrderPaySuccessReq orderPaySuccessReq = new OrderPaySuccessReq();
        orderPaySuccessReq.setOrderId(orderId);
        DubboResponse<Boolean> updateStatusResp = orderCommandProvider.paySuccess(orderPaySuccessReq);
        Boolean updateStatusFlag = updateStatusResp.getData();
        if (updateStatusFlag != null && updateStatusFlag) {
            log.info("更新订单：[{}]支付成功状态", orderId);
            // 生成订单分账规则
            profitSharingBusinessService.saveOrderProfitSharingRule(orderId);
            // 订单明细费用
            saveOrderItemFeeTransaction(orderId);
            return true;
        }
        log.info("更新订单：[{}]支付成功状态失败", orderId);
        return false;
    }

    @Override
    public void saveOrderItemFeeTransaction(Long orderId) {
        log.info("生成费用项明细,orderId={}", orderId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        List<OrderItemFeeTransaction> orderItemFeeTransactionList = orderItemFeeCalculateService.calculateAgentFee(orderItemAndSnapshotDTOList, false, orderDTO.getWarehouseType());
        orderItemFeeTransactionService.saveOrderItemFeeTransaction(orderItemFeeTransactionList);
        log.info("费用项明细生成结束,spent={}", stopwatch.stop());
    }


    @Override
    public ResultDTO<OrderHomeVO> home(LoginContextInfoDTO loginContextInfoDTO) {
        OrderHomeVO orderHomeVO = new OrderHomeVO();
        // 总订单数
        Integer totalNum = countOrderNum(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), null);
        orderHomeVO.setTotalNum(totalNum == null ? 0 : totalNum);

        // 待付款
        Integer waitPayNum = countOrderNum(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), Lists.newArrayList(OrderStatusEnum.NO_PAYMENT.getCode()));
        orderHomeVO.setWaitPaymentNum(waitPayNum == null ? 0 : waitPayNum);
        // 待收货
        Integer deliveringNum = countOrderNum(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(),
                Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode()));
        orderHomeVO.setDeliveringNum(deliveringNum == null ? 0 : deliveringNum);
        // 售后
        OrderAfterSaleCountReq afterSaleCountReq = new OrderAfterSaleCountReq();
        afterSaleCountReq.setTenantId(loginContextInfoDTO.getTenantId());
        afterSaleCountReq.setStoreId(loginContextInfoDTO.getStoreId());
        afterSaleCountReq.setStatusList(Lists.newArrayList(
                OrderAfterSaleStatusEnum.UNAUDITED.getValue(),
                OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
                OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(),
                OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
                OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())
        );
        // TODO:[zhoujiachen]
        Integer afterSaleNum = RpcResultUtil.handle(orderAfterSaleQueryProvider.countOrderAfterSale(afterSaleCountReq));
//        Integer afterSaleNum = orderAfterSaleService.queryAfterSaleNum(loginContextInfoDTO);
        orderHomeVO.setAfterSaleNum(afterSaleNum == null ? 0 : afterSaleNum);

        // 计划单待确认数量
        orderHomeVO.setWaitConfirmPlanOrderNum(planOrderService.countWaitConfirmPlanOrderNum(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId()));
        return ResultDTO.success(orderHomeVO);
    }

    private Integer countOrderNum(Long tenantId, Long storeId, List<Integer> statusList) {
        OrderCountReq countReq = new OrderCountReq();
        countReq.setStatusList(statusList);
        countReq.setStoreId(storeId);
        countReq.setTenantId(tenantId);
        return RpcResultUtil.handle(orderStatisticsQueryProvider.countOrderQuantity(countReq));
    }

    @Override
    public void finished(CosfoMessageDTO cosfoMessageDTO) {
        List<String> orderNos = cosfoMessageDTO.getOrderNos();
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (OrderResp orderDTO : orderList) {
            OrderConfirmReq orderConfirmReq = new OrderConfirmReq();
            orderConfirmReq.setOrderId(orderDTO.getId());
            orderConfirmReq.setTenantId(orderDTO.getTenantId());
            orderCommandProvider.confirm(orderConfirmReq);
        }
    }

    @Override
    public void finishedByMessage(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage) {
        List<String> orderNos = Lists.newArrayList(commonFulfillmentFinishMessage.getSourceOrderNo());
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (OrderResp orderDTO : orderList) {
            OrderConfirmReq orderConfirmReq = new OrderConfirmReq();
            orderConfirmReq.setOrderId(orderDTO.getId());
            orderConfirmReq.setTenantId(orderDTO.getTenantId());
            orderCommandProvider.confirm(orderConfirmReq);
        }

        // 配送完成通知第三方
        orderNotifyBizService.deliveryFinishedNotifyThirdPart(commonFulfillmentFinishMessage);

        // 若需要,创建售后单
        createAfterSaleOrderIfNeed(commonFulfillmentFinishMessage, orderList);
    }

    /**
     * 处理所有订单的售后生成函数
     */
    public Consumer<CommonFulfillmentFinishMessage> createAfterSaleOrderConsumer = new Consumer<CommonFulfillmentFinishMessage>() {
        @Override
        public void accept(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage) {
            String sourceOrderNo = commonFulfillmentFinishMessage.getSourceOrderNo();
            ArrayList<String> orderNos = Lists.newArrayList(sourceOrderNo);

            List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
            Map<String, Long> orderMap = orderList.stream()
                    .collect(Collectors.toMap(OrderResp::getOrderNo, OrderResp::getId, (v1, v2) -> v1));
            List<Long> orderIds = orderList.stream().map(OrderResp::getId).collect(Collectors.toList());
            OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
            orderItemQueryReq.setOrderIds(orderIds);
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
            Map<Long, List<OrderItemAndSnapshotResp>> orderItemMap = orderItemAndSnapshotDTOList.stream()
                    .collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));

            List<OrderAfterSaleCreateDTO> addList = Lists.newArrayList();
            List<CommonFulfillmentFinishMessageDetail> itemList = commonFulfillmentFinishMessage.getItemList();
            if (CollectionUtil.isEmpty(itemList)) {
                log.warn("异常配送信息不处理 itemList不存在 sourceOrderNo:{},commonFulfillmentFinishMessage:{}", commonFulfillmentFinishMessage.getSourceOrderNo(),
                        JSON.toJSONString(commonFulfillmentFinishMessage));
                return;
            }
            Long orderId = orderMap.get(commonFulfillmentFinishMessage.getSourceOrderNo());
            for (CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail : itemList) {
                if (StringUtils.isEmpty(commonFulfillmentFinishMessageDetail.getItemId()) || NumberConstant.ONE > commonFulfillmentFinishMessageDetail.getShortQuantity()) {
                    log.warn("异常配送信息不处理  sourceOrderNo:{},deliveryPathShortSkuDTO:{}", commonFulfillmentFinishMessage.getSourceOrderNo(),
                            JSON.toJSONString(commonFulfillmentFinishMessageDetail));
                    continue;
                }
                OrderAfterSaleCreateDTO orderAfterSaleDTO = builderOrderAfterSaleList(orderId, commonFulfillmentFinishMessageDetail, orderItemMap);
                if (Objects.isNull(orderAfterSaleDTO)) {
                    log.warn("异常配送信息不处理  sourceOrderNo:{},deliveryPathShortSkuDTO:{}", commonFulfillmentFinishMessage.getSourceOrderNo(),
                            JSON.toJSONString(commonFulfillmentFinishMessageDetail));
                    continue;
                }
                addList.add(orderAfterSaleDTO);
            }

            for (OrderAfterSaleCreateDTO orderAfterSaleDto : addList) {
                // 调用商城的售后接口
                try {
                    Long save = orderAfterSaleService.save(orderAfterSaleDto);
                } catch (Exception e) {
                    log.error("createAfterSaleOrderIfNeed,{}自动发起售后失败,参数orderAfterSaleDto:{},DefaultServiceException：{}", Constants.AUTO_CREATE_AFTER_SALE_PREFIX,
                            JSON.toJSONString(orderAfterSaleDto), e.getMessage(), e);
                }
            }
        }
    };

    /**
     * 若需要,创建售后单
     *
     * @param commonFulfillmentFinishMessage
     */
    public void createAfterSaleOrderIfNeed(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage, List<OrderResp> orderList) {
        String sourceOrderNo = commonFulfillmentFinishMessage.getSourceOrderNo();
        boolean existShortData = CollectionUtil.isNotEmpty(commonFulfillmentFinishMessage.getItemList());
        if (!existShortData || StringUtils.isEmpty(sourceOrderNo)) {
            log.info("{}配送正常无需生成售后单,参数 cosfoMessageDTO：{}", Constants.AUTO_CREATE_AFTER_SALE_PREFIX,
                    JSON.toJSONString(commonFulfillmentFinishMessage));
            return;
        }

        // 校验商城自动补发配置，过滤无需自动售后的订单子项
        List<CommonFulfillmentFinishMessageDetail> commonFulfillmentFinishMessageDetails = filterByTenantAutoCreateResendAfterSaleRule(commonFulfillmentFinishMessage, orderList);
        commonFulfillmentFinishMessage.setItemList(commonFulfillmentFinishMessageDetails);

        // 加锁、幂等
        String redisKey = RedisKeyEnum.C00001.join(sourceOrderNo);
        RLock lock = redissonClient.getLock(redisKey);
        // 未获取到锁，退出
        if (!lock.tryLock()) {
            return;
        }
        try {
            String cacheKey = RedisKeyEnum.C00002.join(sourceOrderNo);
            Object orderResultCache = redisTemplate.opsForValue().get(cacheKey);
            if (Objects.nonNull(orderResultCache)) {
                log.info("createAfterSaleOrderIfNeed cacheKey已存在:{}", cacheKey);
                return;
            }

            createAfterSaleOrderConsumer.accept(commonFulfillmentFinishMessage);
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstant.ONE), NumberConstant.SIX,
                    TimeUnit.HOURS);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据商城自动补发配置，处理自动补发数据
     *
     * @param commonFulfillmentFinishMessage
     * @return
     */
    public List<CommonFulfillmentFinishMessageDetail> filterByTenantAutoCreateResendAfterSaleRule(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage, List<OrderResp> orderList) {
        if (CollectionUtil.isEmpty(orderList) || CollectionUtil.isEmpty(commonFulfillmentFinishMessage.getItemList())) {
            return commonFulfillmentFinishMessage.getItemList();
        }
        OrderResp orderDTO = orderList.get(NumberConstant.ZERO);

        // 查询商城自动配置
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(orderDTO.getTenantId(), TenantConfigKeyEnum.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getConfigKey());
        String autoCreateResendAftersaleRule = Optional.ofNullable(tenantCommonConfig).map(TenantCommonConfig::getConfigValue)
                .orElse(TenantConfigKeyEnum.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getDefaultValue());
        // 若是默认开启的状态,不进行处理
        if (TenantConfigKeyEnum.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getDefaultValue().equals(autoCreateResendAftersaleRule)) {
            log.info("租户:{},自营货品，代仓货品，司机配送到店缺货时，自动补发开启", orderDTO.getTenantId());
            return commonFulfillmentFinishMessage.getItemList();
        }

        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(Collections.singletonList(orderDTO.getId()));
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
        Map<Long, OrderItemAndSnapshotResp> itemIdMap = orderItemAndSnapshotDTOList.stream()
                .collect(Collectors.toMap(OrderItemAndSnapshotResp::getItemId, Function.identity(), (v1, v2) -> v1));

        log.info("订单:{},自营货品，代仓货品，自动补发过滤前,commonFulfillmentFinishMessage:{}", orderDTO.getOrderNo(), JSON.toJSONString(commonFulfillmentFinishMessage));
        // 过滤出鲜沐直供的类型
        List<CommonFulfillmentFinishMessageDetail> commonFulfillmentFinishMessageDetails = commonFulfillmentFinishMessage.getItemList().stream().filter(detail -> {
                    String itemId = detail.getItemId();
                    OrderItemAndSnapshotResp orderItemAndSnapshotDTO = itemIdMap.get(Long.valueOf(itemId));
                    return Objects.nonNull(orderItemAndSnapshotDTO) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemAndSnapshotDTO.getGoodsType());
                }
        ).collect(Collectors.toList());
        log.info("订单:{},自营货品，代仓货品，自动补发过滤后,commonFulfillmentFinishMessage:{}", orderDTO.getOrderNo(), JSON.toJSONString(commonFulfillmentFinishMessageDetails));
        return commonFulfillmentFinishMessageDetails;
    }


    private OrderAfterSaleCreateDTO builderOrderAfterSaleList(Long orderId, CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail,
                                                              Map<Long, List<OrderItemAndSnapshotResp>> orderItemMap) {
        String itemId = commonFulfillmentFinishMessageDetail.getItemId();
        Integer shortQuantity = commonFulfillmentFinishMessageDetail.getShortQuantity();
        List<OrderItemAndSnapshotResp> orderItems = orderItemMap.get(orderId);
        if (CollectionUtils.isEmpty(orderItems)) {
            return null;
        }
        Map<Long, OrderItemAndSnapshotResp> itemIdMap = orderItems.stream()
                .collect(Collectors.toMap(OrderItemAndSnapshotResp::getItemId, Function.identity(), (v1, v2) -> v1));
        OrderItemAndSnapshotResp orderItem = itemIdMap.get(Long.valueOf(itemId));
        if (Objects.isNull(orderItem)) {
            return null;
        }
        if (orderItem.getAmount() < shortQuantity) {
            log.error("异常配送信息 缺货数量大于订单数据 orderItem:{},commonFulfillmentFinishMessageDetail:{}", JSON.toJSONString(orderItem),
                    JSON.toJSONString(commonFulfillmentFinishMessageDetail));
            return null;
        }

        // 退货数量
        OrderAfterSaleCreateDTO orderAfterSaleDto = new OrderAfterSaleCreateDTO();
        orderAfterSaleDto.setOrderItemId(orderItem.getOrderItemId());
        orderAfterSaleDto.setAmount(shortQuantity);
        orderAfterSaleDto.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
        Integer serviceType = OrderAfterSaleServiceTypeEnum.RESEND.getValue();
        orderAfterSaleDto.setServiceType(serviceType);
        orderAfterSaleDto.setOrderId(orderItem.getOrderId());
        orderAfterSaleDto.setTenantId(orderItem.getTenantId());
        // 不需要金额
        orderAfterSaleDto.setApplyPrice(ZERO);
        orderAfterSaleDto.setReason(Constants.AFTER_SALE_STOCK_OUT_REASON);
        orderAfterSaleDto.setProofPicture(org.apache.commons.lang3.StringUtils.EMPTY);
        return orderAfterSaleDto;
    }


    @Override
    public ResultDTO<OrderVO> getOrderInfo(String orderNo, Long tenantId) {
//        Order order = orderMapper.selectByOrderNO(orderNo, tenantId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryByNo(orderNo));
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantId);
        loginContextInfoDTO.setStoreId(orderDTO.getStoreId());
        loginContextInfoDTO.setAccountId(orderDTO.getAccountId());
        return detail(orderDTO.getId(), loginContextInfoDTO);
    }


    @Override
    public List<OrderResp> querySameDeliveryOrders(OrderQueryDTO queryDTO) {
        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setStatusList(SAME_DELIVERY_ORDER_STATUS);
        queryReq.setNeOrderId(queryDTO.getId());
        queryReq.setTenantId(queryDTO.getTenantId());
        queryReq.setStoreIds(Lists.newArrayList(queryDTO.getStoreId()));
        queryReq.setSupplierTenantId(queryDTO.getSupplierTenantId());
        queryReq.setDeliveryTime(queryDTO.getDeliveryTime());

        List<OrderResp> result = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
        return result;
    }

    @Override
    public ResultDTO<OrderResp> queryOne(OrderQueryDTO queryDTO) {
        // 根据售后单获取订单
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(queryDTO.getAfterSaleOrderNo())));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            throw new BizException("售后单不存在");
        }
        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);


        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(afterSaleDTO.getOrderId()));

        return ResultDTO.success(orderDTO);
    }

    @Override
    public Boolean closeOrder(Long orderId, LoginContextInfoDTO contextInfoDTO) {
//        orderAfterSaleServiceFacade.closeOrder(orderId, contextInfoDTO.getTenantId());
        OrderCloseReq orderCloseReq = new OrderCloseReq();
        orderCloseReq.setOrderId(orderId);
        orderCloseReq.setTenantId(contextInfoDTO.getTenantId());
        return RpcResultUtil.handle(orderCommandProvider.close(orderCloseReq));
    }


    @Override
    public void updatePayType(Long id, Integer type, Integer onlinePayChannel) {
        OrderUpdateReq update = new OrderUpdateReq();
        update.setId(id);
        update.setPayType(type);
        update.setOnlinePayChannel(onlinePayChannel);
        RpcResultUtil.handle(orderCommandProvider.updatePayType(update));
    }

    @Override
    public void orderOccupyBySpecifyWarehouseAndSku(OrderSelfSupplyOccupyDTO orderSelfSupplyOccupyDTO) {
        // 查询订单信息
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryByNo(orderSelfSupplyOccupyDTO.getOrderNo()));
        // 查询订单项信息
        if (Objects.isNull(orderDTO)) {
            log.warn("订单{}不存在", orderSelfSupplyOccupyDTO.getOrderNo());
            throw new BizException(orderSelfSupplyOccupyDTO.getOrderNo() + "订单不存在");
        }

        if (!OrderStatusEnum.CREATING_ORDER.getCode().equals(orderDTO.getStatus())) {
            return;
        }
        // 查询订单项
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));
        List<OrderItemDTO> orderItemDTOS = OrderItemConverter.INSTANCE.snapshotToItemDTOList(orderItemAndSnapshotDTOList);
        List<OrderItemSnapshotResp> snapshotList = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemIds(orderItemAndSnapshotDTOList.stream().map(OrderItemAndSnapshotResp::getOrderItemId).collect(Collectors.toList())));
        Map<Long, OrderItemSnapshotResp> snapshotMap = snapshotList.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, s -> s));
        List<Long> skuIds = snapshotList.stream().map(OrderItemSnapshotResp::getSkuId).collect(Collectors.toList());
        List<ProductAgentSkuDTO> productAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(skuIds, orderDTO.getTenantId());
        Map<Long, ProductAgentSkuDTO> productAgentSkuDTOMap = productAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        orderItemDTOS.forEach(orderItemDTO -> {
            OrderItemSnapshotResp orderItemSnapshotDTO = snapshotMap.get(orderItemDTO.getId());
            ProductAgentSkuDTO productAgentSkuDTO = productAgentSkuDTOMap.get(orderItemSnapshotDTO.getSkuId());
            orderItemDTO.setSupplySku(productAgentSkuDTO.getAgentSkuCode());
        });

        OrderDTO orderDto = new OrderDTO();
        orderDto.setId(orderDTO.getId());
        orderDto.setTenantId(orderDTO.getTenantId());
        orderDto.setStoreId(orderDTO.getStoreId());
        orderDto.setOrderNo(orderDTO.getOrderNo());
        orderDto.setWarehouseNo(Long.valueOf(orderDTO.getWarehouseNo()));
        orderDto.setOrderItemDTOList(orderItemDTOS);
        stockService.orderOccupyBySpecifyWarehouseAndSku(orderDto);
    }

    @Override
    public List<OrderDeliveryVO> deliveredDetails(Long orderId) {
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));

        // 三方仓订单 城配履约 不展示快递信息（即使有）
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType()) && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(orderDTO.getFulfillmentType())) {
            return Collections.emptyList();
        }

        // 查询订单项
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));
        Map<Long, OrderItemAndSnapshotResp> orderItemVoMap = orderItemAndSnapshotDTOList.stream()
                .collect(Collectors.toMap(OrderItemAndSnapshotResp::getItemId, item -> item));

        List<FulfillmentDeliveryVO> fulfillmentDeliveryVOS = ofcServiceFacade.queryDelivery(Arrays.asList(orderDTO.getOrderNo()));
        if (CollectionUtils.isEmpty(fulfillmentDeliveryVOS)) {
            // 如果订单没有配送记录，则返回空数组；
            log.warn("订单暂无配送记录:{}", orderId);
            return Collections.emptyList();
        }

        // 按照配送号进行分组
        Map<String, List<FulfillmentDeliveryVO>> listMap = fulfillmentDeliveryVOS.stream()
                .collect(Collectors.groupingBy(FulfillmentDeliveryVO::getBatchNo));

        // 三方仓订单快递履约 配送信息 仅显示快递单号和快递公司
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType()) && FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(orderDTO.getFulfillmentType())){
            return listMap.values().stream().map(list -> {
                FulfillmentDeliveryVO fulfillmentDeliveryVO = list.get(NumberConstant.ZERO);
                OrderDeliveryVO orderDeliveryVO = new OrderDeliveryVO();
                orderDeliveryVO.setOrderId(orderDTO.getId());
                orderDeliveryVO.setDeliveryType(fulfillmentDeliveryVO.getDeliveryType());
                orderDeliveryVO.setDeliveryCompany(fulfillmentDeliveryVO.getLogisticsCompany());
                orderDeliveryVO.setDeliveryNo(fulfillmentDeliveryVO.getLogisticsNo());
                return orderDeliveryVO;
            }).collect(Collectors.toList());
        }

        // 查询自提信息
        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = orderSelfLiftingMapper.selectByOrderNo(orderDTO.getOrderNo());
        if (!CollectionUtils.isEmpty(orderSelfLiftingDTOS)) {
            OrderSelfLiftingDTO orderSelfLiftingDTO = orderSelfLiftingDTOS.get(NumberConstant.ZERO);
            if (Objects.isNull(orderSelfLiftingDTO.getActualTime())) {
                log.warn("订单的自提未完成（实际自提时间为空）:{}", orderSelfLiftingDTO);
                return Collections.emptyList();
            }
            WarehouseStorageVO warehouseStorageVO = wncServiceFacade.queryOneWarehouseStorage(orderSelfLiftingDTO.getWarehouseNo());
            OrderDeliveryVO orderDeliveryVO = new OrderDeliveryVO();
            orderDeliveryVO.setOrderId(orderDTO.getId());
            orderDeliveryVO.setDeliveryTime(orderSelfLiftingDTO.getActualTime());
            // TODO 自提为2
            orderDeliveryVO.setDeliveryType(2);
            orderDeliveryVO.setWarehouseNo(orderDTO.getWarehouseNo() == null ? null : Long.valueOf(orderDTO.getWarehouseNo()));
            orderDeliveryVO.setSelfLiftingAddress(orderSelfLiftingDTO.getAddress());
            orderDeliveryVO.setWarehouseName(warehouseStorageVO.getWarehouseName());
            orderDeliveryVO.setContact(warehouseStorageVO.getPersonContact());
            orderDeliveryVO.setPhone(warehouseStorageVO.getPhone());
            // 配送明细
            List<OrderDeliveryItemVO> orderDeliveryItemVOS = orderItemAndSnapshotDTOList.stream().map(orderItemVO -> {
                // 商品信息
                OrderDeliveryItemVO orderDeliveryItemVO = fillOrderItemInfo(orderItemVO);
                orderDeliveryItemVO.setDeliveryQuantity(orderItemVO.getAmount());
                return orderDeliveryItemVO;
            }).collect(Collectors.toList());
            orderDeliveryVO.setOrderDeliveryItemVOList(orderDeliveryItemVOS);
            return Arrays.asList(orderDeliveryVO);
        }

        ArrayList<List<FulfillmentDeliveryVO>> lists = new ArrayList<>(listMap.values());
        List<OrderDeliveryVO> orderDeliveryVOS = lists.stream().map(list -> {
            FulfillmentDeliveryVO fulfillmentDeliveryVO = list.get(NumberConstant.ZERO);
            OrderDeliveryVO orderDeliveryVO = new OrderDeliveryVO();
            orderDeliveryVO.setDeliveryNo(fulfillmentDeliveryVO.getLogisticsNo());
            orderDeliveryVO.setDeliveryCompany(fulfillmentDeliveryVO.getLogisticsCompany());
            orderDeliveryVO.setOrderId(orderDTO.getId());
            orderDeliveryVO.setDeliveryTime(TimeUtils.dateConvertLocalDateTime(fulfillmentDeliveryVO.getCreateTime()));
            orderDeliveryVO.setDeliveryType(fulfillmentDeliveryVO.getDeliveryType());
            orderDeliveryVO.setRemark(fulfillmentDeliveryVO.getRemark());
            orderDeliveryVO.setWarehouseNo(orderDTO.getWarehouseNo() == null ? null : Long.valueOf(orderDTO.getWarehouseNo()));
            // 配送明细
            List<OrderDeliveryItemVO> orderDeliveryItemVOS = list.stream().map(item -> {

                OrderItemAndSnapshotResp orderItemVO = orderItemVoMap.get(Long.valueOf(item.getItemId()));
                // 商品信息
                OrderDeliveryItemVO orderDeliveryItemVO = fillOrderItemInfo(orderItemVO);
                orderDeliveryItemVO.setDeliveryQuantity(item.getQuantity());
                return orderDeliveryItemVO;
            }).collect(Collectors.toList());

            orderDeliveryVO.setOrderDeliveryItemVOList(orderDeliveryItemVOS);
            return orderDeliveryVO;
        }).collect(Collectors.toList());
        return orderDeliveryVOS;
    }

    private static OrderDeliveryItemVO fillOrderItemInfo(OrderItemAndSnapshotResp orderItemVO) {
        OrderDeliveryItemVO orderDeliveryItemVO = new OrderDeliveryItemVO();
        orderDeliveryItemVO.setMainPicture(orderItemVO.getMainPicture());
        orderDeliveryItemVO.setSpecification(orderItemVO.getSpecification());
        orderDeliveryItemVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
        orderDeliveryItemVO.setTitle(orderItemVO.getTitle());
        orderDeliveryItemVO.setItemId(orderItemVO.getItemId());
        return orderDeliveryItemVO;
    }

    /**
     * 再来一单
     *
     * @param orderId
     */
    @Override
    public List<Long> again(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询订单
//        Order order = orderMapper.selectByPrimaryKey(orderId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        // 查询订单项
//        List<OrderItem> orderItems = orderItemMapper.selectByOrderId(order.getTenantId(), order.getId());
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        Map<Long, Integer> itemAmountMap = orderItemAndSnapshotDTOList.stream()
                .collect(Collectors.toMap(OrderItemAndSnapshotResp::getItemId, OrderItemAndSnapshotResp::getAmount));
        // 查询商品信息
        List<Long> itemIds = orderItemAndSnapshotDTOList.stream().map(OrderItemAndSnapshotResp::getItemId).collect(Collectors.toList());

        // 查询商品信息
        List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(itemIds, orderDTO.getTenantId());
        // 只新增上架的商品
        // 有效商品
        List<MarketItemVO> itemVOS = marketItemVOS.stream().filter(
                marketItemVO -> !Objects.equals(marketItemVO.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag()) && MarketItemSaleStatusEnum.ON_SALE.getCode()
                        .equals(marketItemVO.getOnSale())).collect(Collectors.toList());
        // 剔除购物车已经有的商品
        List<Trolley> trolleysList = trolleyMapper.selectByAccountId(loginContextInfoDTO.getTenantId(),
                loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId());
        Map<Long, Trolley> trolleyMap = trolleysList.stream()
                .collect(Collectors.toMap(Trolley::getItemId, item -> item));
        List<Long> trolleyItemIds = trolleysList.stream().map(Trolley::getItemId).collect(Collectors.toList());

        List<Long> resultList = itemVOS.stream().map(MarketItemVO::getItemId).collect(Collectors.toList());
        List<Trolley> trolleys = itemVOS.stream().map(item -> {
            if (!trolleyItemIds.contains(item.getItemId())) {
                Trolley trolley = new Trolley();
                trolley.setAccountId(loginContextInfoDTO.getAccountId());
                trolley.setStoreId(loginContextInfoDTO.getStoreId());
                trolley.setItemId(item.getItemId());
                trolley.setTenantId(loginContextInfoDTO.getTenantId());
                trolley.setAmount(itemAmountMap.get(item.getItemId()));
                return trolley;
                // 有就update
            } else {
                Trolley trolley = trolleyMap.get(item.getItemId());
                trolley.setAmount(trolley.getAmount() + itemAmountMap.get(item.getItemId()));
                trolleyMapper.updateByPrimaryKeySelective(trolley);
            }

            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(trolleys)) {
            trolleyMapper.batchInsert(trolleys);
        }

        return resultList;
    }

    @Override
    public List<OrderDeliveryItemVO> pendingDeliveryDetails(Long orderId) {
        // 查询订单
//        Order order = orderMapper.selectByPrimaryKey(orderId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        // 查询订单项
//        List<OrderItemVO> orderItemVOS = orderItemMapper.queryOrderItemVOByOrderId(order.getTenantId(), order.getId());
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        Map<Long, OrderItemAndSnapshotResp> orderItemVoMap = orderItemAndSnapshotDTOList.stream()
                .collect(Collectors.toMap(OrderItemAndSnapshotResp::getItemId, item -> item));
        List<FulfillmentDeliveryVO> fulfillmentDeliveryVOS = ofcServiceFacade
                .queryWaitDelivery(Arrays.asList(orderDTO.getOrderNo()));
        if (CollectionUtils.isEmpty(fulfillmentDeliveryVOS)) {
            return new ArrayList<>();
        }

        // 配送明细
        List<OrderDeliveryItemVO> orderDeliveryItemVOS = fulfillmentDeliveryVOS.stream().map(item -> {
            OrderDeliveryItemVO orderDeliveryItemVO = new OrderDeliveryItemVO();
            OrderItemAndSnapshotResp orderItemVO = orderItemVoMap.get(Long.valueOf(item.getItemId()));
            orderDeliveryItemVO.setDeliveryQuantity(item.getQuantity());
            // 商品信息
            orderDeliveryItemVO.setMainPicture(orderItemVO.getMainPicture());
            orderDeliveryItemVO.setSpecification(orderItemVO.getSpecification());
            orderDeliveryItemVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
            orderDeliveryItemVO.setTitle(orderItemVO.getTitle());
            orderDeliveryItemVO.setItemId(orderItemVO.getItemId());
            return orderDeliveryItemVO;
        }).collect(Collectors.toList());
        return orderDeliveryItemVOS;
    }

    @Override
    public void orderOutOfStorage(CommonFulfillmentFinishMessage fulfillmentOrderResultMessageDTO) {
        // 查询订单信息
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryByNo(fulfillmentOrderResultMessageDTO.getSourceOrderNo()));
        if (orderDTO == null) {
            log.error("订单出库完成消息消费异常，订单不存在，message：{}", JSON.toJSONString(fulfillmentOrderResultMessageDTO), new ProviderException("订单出库完成消息消费异常"));
            return;
        }
        String orderNo = orderDTO.getOrderNo();
        // 自营仓订单、三方仓订单，为待收货状态,并且是自提订单,此时更新为已完成，同时更新实际自提时间
        // 三方仓自提 城配履约 更新为已完成
        if (OrderStatusEnum.DELIVERING.getCode().equals(orderDTO.getStatus())
                && (OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderDTO.getWarehouseType())
                || (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())) && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(orderDTO.getFulfillmentType()))) {
            List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = orderSelfLiftingMapper.selectByOrderNo(orderDTO.getOrderNo());
            if (!CollectionUtils.isEmpty(orderSelfLiftingDTOS)) {
                if (fulfillmentOrderResultMessageDTO.getWarehouseNo() != null) {
                    OrderSelfLifting orderSelfLifting = new OrderSelfLifting();
                    orderSelfLifting.setOrderNo(orderNo);
                    // 设置更新实际自提时间
                    orderSelfLifting.setWarehouseNo(fulfillmentOrderResultMessageDTO.getWarehouseNo());
                    orderSelfLifting.setActualTime(LocalDateTime.now());
                    orderSelfLiftingMapper.updateActualTime(orderSelfLifting);
                    log.info("订单：{} 更新了仓库：{}的实际自提时间为：{}", orderNo, orderSelfLifting.getWarehouseNo(), orderSelfLifting.getActualTime());
                }

                OrderSelfLiftingFinishReq req = new OrderSelfLiftingFinishReq();
                req.setOrderId(orderDTO.getId());
                RpcResultUtil.handle(orderCommandProvider.selfLiftingFinish(req));
                return;
            }
        }

        // 三方仓订单，10-待出库状态，收到出库完成消息，更新订单为4-待收货
        if(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType()) && OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderDTO.getStatus())) {
            orderService.updateOrderByOutboundTaskCreate(Collections.singletonList(orderDTO.getOrderNo()));
            return;
        }


        // 自营仓订单，非自提订单，改待收货
        // 订单为待出库进行处理
        if ((OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderDTO.getWarehouseType())
                && OrderStatusEnum.OUT_OF_STORAGE.getCode().equals(orderDTO.getStatus()))
                || (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderDTO.getWarehouseType())
                && OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode().equals(orderDTO.getStatus()))) {
            // 自营仓订单出库中改成待收货
            OrderStatusUpdateReq statusUpdateReq = new OrderStatusUpdateReq();
            statusUpdateReq.setOrderId(orderDTO.getId());
            statusUpdateReq.setOriginStatus(orderDTO.getStatus());
            statusUpdateReq.setStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.DELIVERING.getCode());
            Boolean result = RpcResultUtil.handle(orderCommandProvider.updateStatus(statusUpdateReq));
            if (!result) {
                throw new ProviderException(orderDTO.getId() + "订单出库完成，状态变更为待收货失败");
            }
        }
    }

    @Override
    public void doConfirmProfitSharing(Long orderId) {
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        Long tenantId = orderDTO.getTenantId();
        log.info("开始处理订单是否更新分账订单状态，orderId:{}", orderId);
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotService.queryByTenantIdAndOrderId(tenantId, orderId);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(billProfitSharingSnapshots)) {
            log.error("未查询到订单{}分账信息，流程结束", orderId);
            return;
        }
        BillProfitSharingSnapshot snapshot = billProfitSharingSnapshots.get(0);
        if (!Objects.equals(snapshot.getDeliveryType(), ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType())) {
            // 暂时只处理非无仓订单
            log.info("非无仓订单{}，流程结束", orderId);
            return;
        }
        // 查出已经完成配送的供应商
        Pair<Boolean, List<Long>> pair = searchSupplierIdsCompletedDelivery(orderId);
        List<Long> supplierIds = pair.getValue();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(supplierIds)) {
            log.info("orderId:{}，暂时还未有供应商配送完毕", orderId);
            return;
        }
        BillProfitSharingSnapshot billProfitSharingSnapshot = billProfitSharingSnapshots.get(0);
        Integer deliveryType = billProfitSharingSnapshot.getDeliveryType();
        if (!Objects.equals(deliveryType, ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType())) {
            log.info("该订单:{}，快照非无仓订单，流程结束", orderId);
            return;
        }

        Boolean totalSupplierCompleteDeliveryFlag = pair.getKey();
        List<BillProfitSharingOrder> billProfitSharingOrders = billProfitSharingOrderService.queryByTenantAndOrderId(tenantId, orderId);
        for (BillProfitSharingOrder billProfitSharingOrder : billProfitSharingOrders) {
            if (!Objects.equals(billProfitSharingOrder.getStatus(), BillProfitSharingOrderStatusEnum.INIT.getStatus())) {
                log.info("订单:{}，供应商:{}，非初始化状态，流程结束", orderId, billProfitSharingOrder.getSupplierId());
                continue;
            }
            if (Objects.equals(billProfitSharingOrder.getProfitSharingType(), ProfitSharingRuleTypeEnum.DELIVERY.getCode()) && totalSupplierCompleteDeliveryFlag) {
                log.info("订单:{}已全部配送完毕，即将更新分账订单状态", orderId);
                billProfitSharingOrderService.updateStatusById(billProfitSharingOrder.getId(), BillProfitSharingOrderStatusEnum.WAITING.getStatus(), BillProfitSharingOrderStatusEnum.INIT.getStatus());
                continue;
            }

            Long supplierId = billProfitSharingOrder.getSupplierId();
            if (!supplierIds.contains(supplierId)) {
                continue;
            }
            log.info("订单:{}，供应商:{}，即将更新分账订单状态", orderId, supplierId);
            billProfitSharingOrderService.updateStatusById(billProfitSharingOrder.getId(), BillProfitSharingOrderStatusEnum.WAITING.getStatus(), BillProfitSharingOrderStatusEnum.INIT.getStatus());
        }
        log.info("开始处理订单是否更新分账订单状态完毕，orderId:{}", orderId);
    }

    private Pair<Boolean, List<Long>> searchSupplierIdsCompletedDelivery(Long orderId) {
        List<OrderItemAndSnapshotResp> orderItems = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        if (CollectionUtil.isEmpty(orderItems)) {
            log.error("未查询到订单明细信息, orderId={}", orderId);
            return Pair.of(false, Collections.emptyList());
        }

        // 查询订单下未到货退款数量map
        Long tenantId = orderItems.get(0).getTenantId();
        Map<Long, Integer> orderAfterSaleAmtMap = getOrderAfterSaleAmtMapByOrderId(tenantId, orderId);
        List<Long> supplierIds = orderItems.stream()
                .collect(Collectors.groupingBy(
                        OrderItemAndSnapshotResp::getSupplierTenantId,
                        Collectors.mapping(
                                item -> {
                                    // 应配 = 下单数 - 未到货退款数
                                    int actualDeliveryQuantity = item.getDeliveryQuantity();
                                    int afterSaleAmount = orderAfterSaleAmtMap.getOrDefault(item.getOrderItemId(), 0);
                                    int expectedDeliveryQuantity = item.getAmount() - afterSaleAmount;
                                    return actualDeliveryQuantity == expectedDeliveryQuantity;
                                },
                                Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().allMatch(Boolean::booleanValue))
                        )
                ))
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        long totalSupplierCounts = orderItems.stream()
                .filter(el -> Objects.nonNull(el.getSupplierTenantId()))
                .map(OrderItemAndSnapshotResp::getSupplierTenantId)
                .distinct()
                .count();
        return Pair.of(totalSupplierCounts == supplierIds.size(), supplierIds);
    }

    private Map<Long, Integer> getOrderAfterSaleAmtMapByOrderId(Long tenantId, Long orderId) {
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setTenantId(tenantId);
        orderAfterSaleQueryReq.setOrderIds(Collections.singletonList(orderId));
        orderAfterSaleQueryReq.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
        orderAfterSaleQueryReq.setStatusList(Arrays.asList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), OrderAfterSaleStatusEnum.REFUNDING.getValue()));
        List<OrderAfterSaleResp> orderAfterSaleDTOS = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(orderAfterSaleQueryReq));
        return orderAfterSaleDTOS.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId,
                Collectors.summingInt(OrderAfterSaleResp::getAmount)));
    }
}
