package com.cosfo.mall.common.constants;

import cn.hutool.core.lang.Pair;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.exception.ProviderException;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-09-08
 **/

@Getter
@AllArgsConstructor
public enum PayCodeEnum {

    WECHAT_PAY("10", "微信直连"),
    HF_WECHAT_PAY("11", "汇付微信间连"),
    BILL_PAY("20", "账期支付"),
    BALANCE_PAY("30", "余额支付"),
    HF_ALI_PAI("41", "汇付支付宝间连"),
    ZERO_PRICE_PAY("50", "无需支付"),
    OFFLINE_PAY("60", "线下支付"),
    HF_WECHAT_PLUGIN_PAY("111", "汇付插件-微信间连"),
    NON_CASH_PAY("70", "非现金支付"),
    COMBINED_PAY_WX("80", "组合支付"),
    COMBINED_PAY_HF("81", "组合支付"),
    COMBINED_PAY_HF_PLUGIN("811", "组合支付"),
    ;

    /**
     * 支付码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    public static PayCodeEnum getByCode(String code) {
        return Arrays.stream(PayCodeEnum.values()).filter(el -> Objects.equals(el.getCode(), code)).findFirst().orElseThrow(() -> new ProviderException("不合法的支付类型"));
    }


    /**
     * 确定支付方式 payType + onlinePaymentChannel
     * 10 微信支付
     * 11 汇付-微信支付
     * 20 账期支付
     * 30 余额支付
     * 41 汇付-支付宝支付
     * 50 无需支付
     * 60 线下支付
     * 70 非现金支付
     * 80 组合支付
     * 81 组合支付
     */
    public static Pair<String, String> getPayCode(Integer payType, Integer onlinePaymentChannel, boolean h5Request, Integer payMark) {
        String payCode = String.valueOf(payType) + Optional.ofNullable(onlinePaymentChannel).orElse(0);
        if (PayTypeFlagEnum.APPLET_HUI_FU_PLUGIN_FLAG.getCode().equals(payMark)) {
            payCode = payCode + payMark;
        }
        PayCodeEnum payCodeEnum = getByCode(payCode);
        String tradeType = null;
        switch (payCodeEnum) {
            case WECHAT_PAY:
                tradeType = TradeTypeEnum.JSAPI.getDesc();
                break;
            case HF_WECHAT_PAY:
                if (h5Request) {
                    tradeType = TradeTypeEnum.T_JSAPI.getDesc();
                } else {
                    tradeType = TradeTypeEnum.T_MINIAPP.getDesc();
                }
                break;
            case HF_WECHAT_PLUGIN_PAY:
                tradeType = TradeTypeEnum.HF_WECHAT_PLUGIN.getDesc();
                break;
            case BILL_PAY:
                tradeType = TradeTypeEnum.BILL.getDesc();
                break;
            case BALANCE_PAY:
                tradeType = TradeTypeEnum.BALANCE.getDesc();
                break;
            case HF_ALI_PAI:
                tradeType = TradeTypeEnum.A_NATIVE.getDesc();
                break;
            case ZERO_PRICE_PAY:
                tradeType = TradeTypeEnum.ZERO_PRICE.getDesc();
                break;
            case OFFLINE_PAY:
                tradeType = TradeTypeEnum.OFFLINE_PAY.getDesc();
                break;
            case NON_CASH_PAY:
                tradeType = TradeTypeEnum.NON_CASH_PAY.getDesc();
                break;
            case COMBINED_PAY_WX:
            case COMBINED_PAY_HF:
            case COMBINED_PAY_HF_PLUGIN:
                tradeType = TradeTypeEnum.COMBINED_PAY.getDesc();
                break;

        }
        return Pair.of(payCode, tradeType);
    }

    /**
     * 退款码
     *
     * @param payType
     * @param onlinePaymentChannel
     * @return
     */
    public static String getRefundCode(Integer payType, Integer onlinePaymentChannel, BigDecimal price) {
        if (BigDecimal.ZERO.compareTo(price) == 0) {
            return String.valueOf(PayTypeEnum.ZERO_PRICE_PAY.getType()) + 0;
        }
        return String.valueOf(payType) + Optional.ofNullable(onlinePaymentChannel).orElse(0);
    }
}
